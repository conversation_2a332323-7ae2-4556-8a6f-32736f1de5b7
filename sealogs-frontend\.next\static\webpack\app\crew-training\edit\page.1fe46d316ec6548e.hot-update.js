"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/edit/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/create/form.tsx":
/*!**************************************************!*\
  !*** ./src/app/ui/crew-training/create/form.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_filter_components_crew_dropdown_crew_dropdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../../components/filter/components/crew-dropdown/crew-dropdown */ \"(app-pages-browser)/./src/components/filter/components/crew-dropdown/crew-dropdown.tsx\");\n/* harmony import */ var _components_skeletons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../../components/skeletons */ \"(app-pages-browser)/./src/components/skeletons.tsx\");\n/* harmony import */ var _type_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../type-multiselect-dropdown */ \"(app-pages-browser)/./src/app/ui/crew-training/type-multiselect-dropdown.tsx\");\n/* harmony import */ var _crew_multiselect_dropdown_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../crew/multiselect-dropdown/multiselect-dropdown */ \"(app-pages-browser)/./src/app/ui/crew/multiselect-dropdown/multiselect-dropdown.tsx\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _editor__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../../editor */ \"(app-pages-browser)/./src/app/ui/editor.tsx\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_loading__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/loading */ \"(app-pages-browser)/./src/app/loading.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_sheet__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/ui/sheet */ \"(app-pages-browser)/./src/components/ui/sheet.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_signature_pad__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/signature-pad */ \"(app-pages-browser)/./src/components/signature-pad.tsx\");\n/* harmony import */ var _components_footer_wrapper__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/footer-wrapper */ \"(app-pages-browser)/./src/components/footer-wrapper.tsx\");\n/* harmony import */ var _components_daily_check_field__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/daily-check-field */ \"(app-pages-browser)/./src/components/daily-check-field.tsx\");\n/* harmony import */ var _components_DateRange__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/DateRange */ \"(app-pages-browser)/./src/components/DateRange.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @/components/ui/alert-dialog */ \"(app-pages-browser)/./src/components/ui/alert-dialog.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _queries__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! ./queries */ \"(app-pages-browser)/./src/app/ui/crew-training/create/queries.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst TrainingForm = (param)=>{\n    let { trainingID = 0, memberId = 0, trainingTypeId = 0, vesselId = 0 } = param;\n    var _rawTraining_VesselID, _rawTraining_trainingLocation, _rawTraining_trainingLocation1, _vessels_find;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_25__.useToast)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [training, setTraining] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({});\n    const [rawTraining, setRawTraining] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)();\n    const [trainingDate, setTrainingDate] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(new Date());\n    const [hasFormErrors, setHasFormErrors] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [selectedMemberList, setSelectedMemberList] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [signatureMembers, setSignatureMembers] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [vessels, setVessels] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)();\n    const [trainingTypes, setTrainingTypes] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [openViewProcedure, setOpenViewProcedure] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [currentComment, setCurrentComment] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [currentField, setCurrentField] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [bufferProcedureCheck, setBufferProcedureCheck] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [bufferFieldComment, setBufferFieldComment] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [openCommentAlert, setOpenCommentAlert] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [formErrors, setFormErrors] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        TrainingTypes: \"\",\n        TrainerID: \"\",\n        VesselID: \"\",\n        Date: \"\"\n    });\n    const [vesselID, setVesselID] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(vesselId);\n    const [fieldImages, setFieldImages] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_11__.getTrainingTypes)(setTrainingTypes);\n    const [getFieldImages] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_9__.GET_SECTION_MEMBER_IMAGES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readCaptureImages.nodes;\n            if (data) {\n                setFieldImages(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"getFieldImages error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        getFieldImages({\n            variables: {\n                filter: {\n                    trainingSessionID: {\n                        eq: trainingID\n                    }\n                }\n            }\n        });\n    }, []);\n    const refreshImages = async ()=>{\n        await getFieldImages({\n            variables: {\n                filter: {\n                    trainingSessionID: {\n                        eq: trainingID\n                    }\n                }\n            }\n        });\n    };\n    const handleSetTraining = (training)=>{\n        var _training_procedureFields;\n        const tDate = new Date(training.date);\n        setTrainingDate(tDate);\n        const trainingData = {\n            ID: trainingID,\n            Date: dayjs__WEBPACK_IMPORTED_MODULE_1___default()(training.date).format(\"YYYY-MM-DD\"),\n            Members: training.members.nodes.map((m)=>m.id),\n            TrainerID: training.trainer.id,\n            TrainingSummary: training.trainingSummary,\n            TrainingTypes: training.trainingTypes.nodes.map((t)=>t.id),\n            // VesselID: training.vessel.id,\n            VesselID: training.vesselID\n        };\n        setRawTraining(training);\n        setTraining(trainingData);\n        setContent(training.trainingSummary);\n        const members = training.members.nodes.map((m)=>{\n            var _m_firstName, _m_surname;\n            return {\n                label: \"\".concat((_m_firstName = m.firstName) !== null && _m_firstName !== void 0 ? _m_firstName : \"\", \" \").concat((_m_surname = m.surname) !== null && _m_surname !== void 0 ? _m_surname : \"\"),\n                value: m.id\n            };\n        }) || [];\n        const vesselCrewIds = training.vessel.seaLogsMembers.nodes.map((slm)=>+slm.id);\n        const vesselCrews = members.filter((m)=>vesselCrewIds.includes(+m.value));\n        setSelectedMemberList(vesselCrews);\n        const signatures = training.signatures.nodes.map((s)=>({\n                MemberID: s.member.id,\n                SignatureData: s.signatureData,\n                ID: s.id\n            }));\n        setSignatureMembers(signatures);\n        // Initialize buffer with existing procedure field data for updates\n        if ((_training_procedureFields = training.procedureFields) === null || _training_procedureFields === void 0 ? void 0 : _training_procedureFields.nodes) {\n            const existingProcedureChecks = training.procedureFields.nodes.map((field)=>({\n                    fieldId: field.customisedComponentFieldID,\n                    status: field.status === \"Ok\"\n                }));\n            setBufferProcedureCheck(existingProcedureChecks);\n            const existingFieldComments = training.procedureFields.nodes.filter((field)=>field.comment).map((field)=>({\n                    fieldId: field.customisedComponentFieldID,\n                    comment: field.comment\n                }));\n            setBufferFieldComment(existingFieldComments);\n        }\n    };\n    const handleSetVessels = (data)=>{\n        const activeVessels = data === null || data === void 0 ? void 0 : data.filter((vessel)=>!vessel.archived);\n        const formattedData = [\n            {\n                label: \"Other\",\n                value: \"Other\"\n            },\n            {\n                label: \"Desktop/shore\",\n                value: \"Onshore\"\n            },\n            ...activeVessels.map((vessel)=>({\n                    value: vessel.id,\n                    label: vessel.title\n                }))\n        ];\n        setVessels(formattedData);\n    };\n    const [queryVessels] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_29__.ReadVessels, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (queryVesselResponse)=>{\n            if (queryVesselResponse.readVessels.nodes) {\n                handleSetVessels(queryVesselResponse.readVessels.nodes);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryVessels error\", error);\n        }\n    });\n    const loadVessels = async ()=>{\n        await queryVessels({\n            variables: {\n                limit: 200,\n                offset: 0\n            }\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (isLoading) {\n            loadVessels();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_11__.getTrainingSessionByID)(trainingID, handleSetTraining);\n    const [mutationCreateTrainingSession, { loading: mutationCreateTrainingSessionLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.CREATE_TRAINING_SESSION, {\n        onCompleted: (response)=>{\n            const data = response.createTrainingSession;\n            if (data.id > 0) {\n                if (bufferProcedureCheck.length > 0) {\n                    const procedureFields = bufferProcedureCheck.map((procedureField)=>{\n                        var _bufferFieldComment_find;\n                        return {\n                            status: procedureField.status ? \"Ok\" : \"Not_Ok\",\n                            trainingSessionID: data.id,\n                            customisedComponentFieldID: procedureField.fieldId,\n                            comment: (_bufferFieldComment_find = bufferFieldComment.find((comment)=>comment.fieldId == procedureField.fieldId)) === null || _bufferFieldComment_find === void 0 ? void 0 : _bufferFieldComment_find.comment\n                        };\n                    });\n                    procedureFields.forEach((procedureField)=>{\n                        createCustomisedComponentFieldData({\n                            variables: {\n                                input: procedureField\n                            }\n                        });\n                    });\n                }\n                updateTrainingSessionDues();\n                updateSignatures(data.id);\n                handleEditorChange(data.trainingSummary);\n                router.push(\"/crew-training\");\n            } else {\n                console.error(\"mutationCreateUser error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationCreateTrainingSession error\", error);\n        }\n    });\n    const [mutationUpdateTrainingSession, { loading: mutationUpdateTrainingSessionLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.UPDATE_TRAINING_SESSION, {\n        onCompleted: (response)=>{\n            const data = response.updateTrainingSession;\n            if (data.id > 0) {\n                // Handle procedure checks for updates\n                if (bufferProcedureCheck.length > 0) {\n                    const procedureFields = bufferProcedureCheck.map((procedureField)=>{\n                        var _rawTraining_procedureFields_nodes, _rawTraining_procedureFields, _bufferFieldComment_find;\n                        const existingField = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : (_rawTraining_procedureFields_nodes = _rawTraining_procedureFields.nodes) === null || _rawTraining_procedureFields_nodes === void 0 ? void 0 : _rawTraining_procedureFields_nodes.find((field)=>field.customisedComponentFieldID === procedureField.fieldId);\n                        return {\n                            id: existingField === null || existingField === void 0 ? void 0 : existingField.id,\n                            status: procedureField.status ? \"Ok\" : \"Not_Ok\",\n                            trainingSessionID: data.id,\n                            customisedComponentFieldID: procedureField.fieldId,\n                            comment: (_bufferFieldComment_find = bufferFieldComment.find((comment)=>comment.fieldId == procedureField.fieldId)) === null || _bufferFieldComment_find === void 0 ? void 0 : _bufferFieldComment_find.comment\n                        };\n                    });\n                    procedureFields.forEach((procedureField)=>{\n                        if (procedureField.id) {\n                            // Update existing field\n                            updateCustomisedComponentFieldData({\n                                variables: {\n                                    input: procedureField\n                                }\n                            });\n                        } else {\n                            // Create new field\n                            const { id, ...createInput } = procedureField;\n                            createCustomisedComponentFieldData({\n                                variables: {\n                                    input: createInput\n                                }\n                            });\n                        }\n                    });\n                }\n                updateTrainingSessionDues();\n                updateSignatures(trainingID);\n                handleEditorChange(data.trainingSummary);\n                if (+memberId > 0) {\n                    router.push(\"/crew/info?id=\".concat(memberId));\n                } else if (+vesselId > 0) {\n                    router.push(\"/vessel/info?id=\".concat(vesselId));\n                } else {\n                    router.push(\"/crew-training\");\n                }\n            } else {\n                console.error(\"mutationUpdateTrainingSession error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationUpdateTrainingSession error\", error);\n        }\n    });\n    const [readOneTrainingSessionDue, { loading: readOneTrainingSessionDueLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_9__.READ_ONE_TRAINING_SESSION_DUE, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            return response.readOneTrainingSessionDue.data;\n        },\n        onError: (error)=>{\n            console.error(\"readOneTrainingSessionDueLoading error:\", error);\n            return null;\n        }\n    });\n    const getTrainingSessionDueWithVariables = async function() {\n        let variables = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {}, onCompleted = arguments.length > 1 ? arguments[1] : void 0;\n        const { data } = await readOneTrainingSessionDue({\n            variables: variables\n        });\n        onCompleted(data.readOneTrainingSessionDue);\n    };\n    const [mutationCreateTrainingSessionDue, { loading: createTrainingSessionDueLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.CREATE_TRAINING_SESSION_DUE, {\n        onCompleted: (response)=>{},\n        onError: (error)=>{\n            console.error(\"createTrainingSessionDue error\", error);\n        }\n    });\n    const [mutationUpdateTrainingSessionDue, { loading: updateTrainingSessionDueLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.UPDATE_TRAINING_SESSION_DUE, {\n        onCompleted: (response)=>{},\n        onError: (error)=>{\n            console.error(\"updateTrainingSessionDue error\", error);\n        }\n    });\n    const updateTrainingSessionDues = async ()=>{\n        const trainingSessionDues = [];\n        const vesselID = training.VesselID;\n        training.TrainingTypes.forEach((t)=>{\n            const trainingInfo = trainingTypes.find((tt)=>tt.id === t);\n            if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(trainingInfo) && trainingInfo.occursEvery > 0) {\n                const trainingTypeID = t;\n                const newDueDate = dayjs__WEBPACK_IMPORTED_MODULE_1___default()(training.Date).add(trainingInfo.occursEvery, \"day\");\n                training.Members.forEach((m)=>{\n                    const memberID = m;\n                    trainingSessionDues.push({\n                        dueDate: newDueDate.format(\"YYYY-MM-DD\"),\n                        memberID: memberID,\n                        vesselID: vesselID,\n                        trainingTypeID: trainingTypeID\n                    });\n                });\n            }\n        });\n        let trainingSessionDueWithIDs = [];\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(trainingSessionDues)) {\n            await Promise.all(trainingSessionDues.map(async (item)=>{\n                const variables = {\n                    filter: {\n                        memberID: {\n                            eq: item.memberID\n                        },\n                        vesselID: {\n                            eq: item.vesselID\n                        },\n                        trainingTypeID: {\n                            eq: item.trainingTypeID\n                        }\n                    }\n                };\n                const onCompleted = (response)=>{\n                    var _response_id;\n                    trainingSessionDueWithIDs.push({\n                        ...item,\n                        id: (_response_id = response === null || response === void 0 ? void 0 : response.id) !== null && _response_id !== void 0 ? _response_id : 0\n                    });\n                };\n                await getTrainingSessionDueWithVariables(variables, onCompleted);\n            }));\n        }\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(trainingSessionDueWithIDs)) {\n            await Promise.all(Array.from(trainingSessionDueWithIDs).map(async (item)=>{\n                const variables = {\n                    variables: {\n                        input: item\n                    }\n                };\n                if (item.id === 0) {\n                    await mutationCreateTrainingSessionDue(variables);\n                } else {\n                    await mutationUpdateTrainingSessionDue(variables);\n                }\n            }));\n        }\n    };\n    const [createCustomisedComponentFieldData] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.CREATE_CUSTOMISED_COMPONENT_FIELD_DATA, {\n        onCompleted: (response)=>{\n            var _rawTraining_procedureFields;\n            const data = response.createCustomisedComponentFieldData;\n            if (data.id > 0 && (rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : _rawTraining_procedureFields.nodes)) {\n                setRawTraining({\n                    ...rawTraining,\n                    procedureFields: {\n                        ...rawTraining.procedureFields,\n                        nodes: [\n                            ...rawTraining.procedureFields.nodes,\n                            data\n                        ]\n                    }\n                });\n            } else {\n                console.error(\"createCustomisedComponentFieldData error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"createCustomisedComponentFieldData error\", error);\n        }\n    });\n    const [updateCustomisedComponentFieldData] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.UPDATE_CUSTOMISED_COMPONENT_FIELD_DATA, {\n        onCompleted: (response)=>{\n            const data = response.updateCustomisedComponentFieldData;\n            if (data.id > 0) {\n                var _rawTraining_procedureFields;\n                setRawTraining({\n                    ...rawTraining,\n                    procedureFields: {\n                        ...rawTraining.procedureFields,\n                        nodes: [\n                            ...rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : _rawTraining_procedureFields.nodes.filter((procedureField)=>procedureField.customisedComponentFieldID !== data.customisedComponentFieldID),\n                            {\n                                ...data\n                            }\n                        ]\n                    }\n                });\n            } else {\n                console.error(\"updateCustomisedComponentFieldData error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"updateCustomisedComponentFieldData error\", error);\n        }\n    });\n    const handleSave = async ()=>{\n        var _training_Members, _training_TrainingTypes;\n        let hasErrors = false;\n        let errors = {\n            TrainingTypes: \"\",\n            TrainerID: \"\",\n            VesselID: \"\",\n            Date: \"\"\n        };\n        setFormErrors(errors);\n        if (lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(training.TrainingTypes)) {\n            hasErrors = true;\n            errors.TrainingTypes = \"Nature of training is required\";\n        }\n        if (!(training.TrainerID && training.TrainerID > 0)) {\n            hasErrors = true;\n            errors.TrainerID = \"Trainer is required\";\n        }\n        if (!training.VesselID && !(training.TrainingLocationID && training.TrainingLocationID >= 0)) {\n            hasErrors = true;\n            errors.VesselID = \"Location is required\";\n        }\n        if (typeof training.Date === \"undefined\") {\n            training.Date = dayjs__WEBPACK_IMPORTED_MODULE_1___default()().format(\"YYYY-MM-DD\");\n        }\n        if (training.Date === null || !dayjs__WEBPACK_IMPORTED_MODULE_1___default()(training.Date).isValid()) {\n            hasErrors = true;\n            errors.Date = \"The date is invalid\";\n        }\n        if (hasErrors) {\n            setHasFormErrors(true);\n            setFormErrors(errors);\n            toast({\n                title: \"Error\",\n                description: errors.TrainingTypes || errors.TrainerID || errors.VesselID || errors.Date,\n                variant: \"destructive\"\n            });\n            return;\n        }\n        const input = {\n            id: trainingID,\n            date: training.Date ? dayjs__WEBPACK_IMPORTED_MODULE_1___default()(training.Date).format(\"YYYY-MM-DD\") : \"\",\n            members: (_training_Members = training.Members) === null || _training_Members === void 0 ? void 0 : _training_Members.join(\",\"),\n            trainerID: training.TrainerID,\n            trainingSummary: content,\n            trainingTypes: (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.join(\",\"),\n            vesselID: training === null || training === void 0 ? void 0 : training.VesselID,\n            trainingLocationType: (training === null || training === void 0 ? void 0 : training.VesselID) ? training.VesselID === \"Other\" || training.VesselID === \"Onshore\" ? training.VesselID : \"Vessel\" : \"Location\"\n        };\n        if (trainingID === 0) {\n            await mutationCreateTrainingSession({\n                variables: {\n                    input: input\n                }\n            });\n        } else {\n            await mutationUpdateTrainingSession({\n                variables: {\n                    input: input\n                }\n            });\n        }\n    };\n    // var signatureCount = 0\n    const updateSignatures = (TrainingID)=>{\n        signatureMembers.length > 0 && (signatureMembers === null || signatureMembers === void 0 ? void 0 : signatureMembers.forEach((signature)=>{\n            checkAndSaveSignature(signature, TrainingID);\n        }));\n    };\n    const checkAndSaveSignature = async (signature, TrainingID)=>{\n        await queryGetMemberTrainingSignatures({\n            variables: {\n                filter: {\n                    memberID: {\n                        eq: signature.MemberID\n                    },\n                    trainingSessionID: {\n                        in: TrainingID\n                    }\n                }\n            }\n        }).then((response)=>{\n            const data = response.data.readMemberTraining_Signatures.nodes;\n            if (data.length > 0) {\n                mutationUpdateMemberTrainingSignature({\n                    variables: {\n                        input: {\n                            id: data[0].id,\n                            memberID: signature.MemberID,\n                            signatureData: signature.SignatureData,\n                            trainingSessionID: TrainingID\n                        }\n                    }\n                });\n            } else {\n                if (signature.SignatureData) {\n                    mutationCreateMemberTrainingSignature({\n                        variables: {\n                            input: {\n                                memberID: signature.MemberID,\n                                signatureData: signature.SignatureData,\n                                trainingSessionID: TrainingID\n                            }\n                        }\n                    });\n                }\n            }\n        }).catch((error)=>{\n            console.error(\"mutationGetMemberTrainingSignatures error\", error);\n        });\n    };\n    const [queryGetMemberTrainingSignatures] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_9__.GET_MEMBER_TRAINING_SIGNATURES);\n    const [mutationUpdateMemberTrainingSignature, { loading: mutationUpdateMemberTrainingSignatureLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.UPDATE_MEMBER_TRAINING_SIGNATURE, {\n        onCompleted: (response)=>{\n            const data = response.updateMemberTraining_Signature;\n            if (data.id > 0) {\n            // signatureCount++\n            // if (signatureCount === signatureMembers.length) {\n            // }\n            } else {\n                console.error(\"mutationUpdateMemberTrainingSignature error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationUpdateMemberTrainingSignature error\", error);\n        }\n    });\n    const [mutationCreateMemberTrainingSignature, { loading: mutationCreateMemberTrainingSignatureLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.CREATE_MEMBER_TRAINING_SIGNATURE, {\n        onCompleted: (response)=>{\n            const data = response.createMemberTraining_Signature;\n            if (data.id > 0) {\n            // signatureCount++\n            // if (signatureCount === signatureMembers.length) {\n            // }\n            } else {\n                console.error(\"mutationCreateMemberTrainingSignature error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationCreateMemberTrainingSignature error\", error);\n        }\n    });\n    const handleTrainingDateChange = (date)=>{\n        setTrainingDate(date && new Date(date.toString()));\n        setTraining({\n            ...training,\n            Date: dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"YYYY-MM-DD\")\n        });\n    };\n    const handleTrainerChange = (trainer)=>{\n        if (!trainer) return; // Add early return if trainer is null\n        // Use Set() to prevent duplicate values, then Array.from() to convert it to an array\n        const membersSet = new Set((training === null || training === void 0 ? void 0 : training.Members) || []);\n        membersSet.add(trainer.value);\n        const members = Array.from(membersSet);\n        setTraining({\n            ...training,\n            TrainerID: trainer.value,\n            Members: members\n        });\n        setSelectedMemberList([\n            ...selectedMemberList,\n            trainer\n        ]);\n        setSignatureMembers([\n            ...signatureMembers,\n            {\n                MemberID: +trainer.value,\n                SignatureData: null\n            }\n        ]);\n    };\n    const handleTrainingTypeChange = (trainingTypes)=>{\n        setTraining({\n            ...training,\n            TrainingTypes: trainingTypes.map((item)=>item.value)\n        });\n    };\n    /* const handleTrainingLocationChange = (vessel: any) => {\r\n        setTraining({\r\n            ...training,\r\n            VesselID: vessel.isVessel ? vessel.value : 0,\r\n            TrainingLocationID: !vessel.isVessel ? vessel.value : 0,\r\n        })\r\n    } */ const handleMemberChange = (members)=>{\n        const signatures = signatureMembers.filter((item)=>members.some((m)=>+m.value === item.MemberID));\n        setTraining({\n            ...training,\n            Members: members.map((item)=>item.value)\n        });\n        setSelectedMemberList(members);\n        setSignatureMembers(signatures);\n    };\n    const onSignatureChanged = (e, member, memberId)=>{\n        const index = signatureMembers.findIndex((object)=>object.MemberID === memberId);\n        const updatedMembers = [\n            ...signatureMembers\n        ];\n        if (e) {\n            if (index !== -1) {\n                if (e.trim() === \"\") {\n                    updatedMembers.splice(index, 1);\n                } else {\n                    updatedMembers[index].SignatureData = e;\n                }\n            } else {\n                updatedMembers.push({\n                    MemberID: memberId,\n                    SignatureData: e\n                });\n            }\n        } else {\n            updatedMembers.splice(index, 1);\n        }\n        setSignatureMembers(updatedMembers);\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_11__.getTrainingTypeByID)(trainingTypeId, setTraining);\n    const handleTrainingVesselChange = (vessel)=>{\n        setTraining({\n            ...training,\n            VesselID: vessel ? typeof vessel === \"object\" && !Array.isArray(vessel) ? vessel.value : 0 : 0\n        });\n    };\n    const handleEditorChange = (newContent)=>{\n        setContent(newContent);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(training)) {\n            const vid = vesselId > 0 || isNaN(parseInt(training === null || training === void 0 ? void 0 : training.VesselID, 10)) ? vesselId : parseInt(training === null || training === void 0 ? void 0 : training.VesselID, 10);\n            setVesselID(vid);\n        }\n    }, [\n        vesselId,\n        training\n    ]);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_13__.getPermissions);\n    }, []);\n    if (!permissions || !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_13__.hasPermission)(\"EDIT_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_13__.hasPermission)(\"VIEW_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_13__.hasPermission)(\"RECORD_TRAINING\", permissions)) {\n        return !permissions ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n            lineNumber: 837,\n            columnNumber: 13\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n            errorMessage: \"Oops You do not have the permission to view this section.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n            lineNumber: 839,\n            columnNumber: 13\n        }, undefined);\n    }\n    const getProcedures = ()=>{\n        const procedures = trainingTypes.filter((type)=>{\n            var _training_TrainingTypes;\n            return training === null || training === void 0 ? void 0 : (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.includes(type.id);\n        });\n        return procedures.map((type)=>{\n            var _this;\n            return type.customisedComponentField.nodes.length > 0 ? {\n                id: type.id,\n                title: type.title,\n                fields: (_this = [\n                    ...type.customisedComponentField.nodes\n                ]) === null || _this === void 0 ? void 0 : _this.sort((a, b)=>a.sortOrder - b.sortOrder)\n            } : null;\n        }).filter((type)=>type != null);\n    };\n    const handleProcedureChecks = (field, type, status)=>{\n        // Always use buffer system for consistency, whether creating or updating\n        const procedureCheck = bufferProcedureCheck.filter((procedureField)=>procedureField.fieldId !== field.id);\n        setBufferProcedureCheck([\n            ...procedureCheck,\n            {\n                fieldId: field.id,\n                status: status\n            }\n        ]);\n    };\n    const getFieldStatus = (field)=>{\n        var _rawTraining_procedureFields_nodes, _rawTraining_procedureFields;\n        if (bufferProcedureCheck.length > 0) {\n            const fieldStatus = bufferProcedureCheck.find((procedureField)=>procedureField.fieldId == field.id);\n            if (fieldStatus) {\n                return fieldStatus.status ? \"Ok\" : \"Not_Ok\";\n            }\n        }\n        const fieldStatus = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : (_rawTraining_procedureFields_nodes = _rawTraining_procedureFields.nodes) === null || _rawTraining_procedureFields_nodes === void 0 ? void 0 : _rawTraining_procedureFields_nodes.find((procedureField)=>procedureField.customisedComponentFieldID == field.id);\n        return (fieldStatus === null || fieldStatus === void 0 ? void 0 : fieldStatus.status) || \"\";\n    };\n    const showCommentPopup = (field)=>{\n        var _rawTraining_procedureFields_nodes, _rawTraining_procedureFields;\n        const fieldComment = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : (_rawTraining_procedureFields_nodes = _rawTraining_procedureFields.nodes) === null || _rawTraining_procedureFields_nodes === void 0 ? void 0 : _rawTraining_procedureFields_nodes.find((procedureField)=>procedureField.customisedComponentFieldID == field.id);\n        if (bufferFieldComment.length > 0) {\n            const fieldComment = bufferFieldComment.find((procedureField)=>procedureField.fieldId == field.id);\n            setCurrentComment((fieldComment === null || fieldComment === void 0 ? void 0 : fieldComment.comment) || \"\");\n        } else {\n            setCurrentComment((fieldComment === null || fieldComment === void 0 ? void 0 : fieldComment.comment) || \"\");\n        }\n        setCurrentField(field);\n        setOpenCommentAlert(true);\n    };\n    const getComment = (field)=>{\n        var _rawTraining_procedureFields_nodes, _rawTraining_procedureFields;\n        if (bufferFieldComment.length > 0) {\n            const fieldComment = bufferFieldComment.find((procedureField)=>procedureField.fieldId == field.id);\n            if (fieldComment) {\n                return fieldComment.comment;\n            }\n        }\n        const fieldComment = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : (_rawTraining_procedureFields_nodes = _rawTraining_procedureFields.nodes) === null || _rawTraining_procedureFields_nodes === void 0 ? void 0 : _rawTraining_procedureFields_nodes.find((procedureField)=>procedureField.customisedComponentFieldID == field.id);\n        return (fieldComment === null || fieldComment === void 0 ? void 0 : fieldComment.comment) || field.comment;\n    };\n    const handleSaveComment = ()=>{\n        // Always use buffer system for consistency, whether creating or updating\n        const fieldComment = bufferFieldComment.filter((procedureField)=>procedureField.fieldId !== currentField.id);\n        setBufferFieldComment([\n            ...fieldComment,\n            {\n                fieldId: currentField.id,\n                comment: currentComment\n            }\n        ]);\n        setOpenCommentAlert(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_17__.Card, {\n                className: \"mb-2.5 mx-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_17__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_17__.CardTitle, {\n                            children: [\n                                trainingID === 0 ? \"New\" : \"Edit\",\n                                \" Training Session\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                            lineNumber: 940,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 939,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_18__.Separator, {\n                        className: \"my-5\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 944,\n                        columnNumber: 17\n                    }, undefined),\n                    !training && trainingID > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_5__.TrainingSessionFormSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 946,\n                        columnNumber: 21\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_17__.CardContent, {\n                        className: \"p-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid lg:grid-cols-3 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-3 md:col-span-1 my-4 \",\n                                        children: [\n                                            \"Training Details\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \" mt-4 max-w-[25rem] leading-loose mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 952,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            training && trainingTypes.filter((type)=>{\n                                                var _training_TrainingTypes;\n                                                return (training === null || training === void 0 ? void 0 : (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.includes(type.id)) && type.procedure;\n                                            }).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_20__.Button, {\n                                                onClick: ()=>setOpenViewProcedure(true),\n                                                children: \"View Procedures\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 960,\n                                                columnNumber: 41\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 950,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-3 md:col-span-2 pt-8 pb-5 space-y-6 px-7 border border-border border-dashed rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full my-4 flex flex-col\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                                                label: \"Trainer\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_crew_dropdown_crew_dropdown__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    value: training === null || training === void 0 ? void 0 : training.TrainerID,\n                                                                    vesselID: vesselID,\n                                                                    onChange: handleTrainerChange\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                    lineNumber: 972,\n                                                                    columnNumber: 45\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 971,\n                                                                columnNumber: 41\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                className: \"text-destructive\",\n                                                                children: hasFormErrors && formErrors.TrainerID\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 978,\n                                                                columnNumber: 41\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 970,\n                                                        columnNumber: 37\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full md:mt-4 flex flex-col\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_type_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                value: training === null || training === void 0 ? void 0 : training.TrainingTypes,\n                                                                onChange: handleTrainingTypeChange\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 984,\n                                                                columnNumber: 41\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                className: \"text-red-vivid-500\",\n                                                                children: hasFormErrors && formErrors.TrainingTypes\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 988,\n                                                                columnNumber: 41\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 983,\n                                                        columnNumber: 37\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 969,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full mt-4 flex flex-col\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                                        children: \"Crew\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 995,\n                                                        columnNumber: 37\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_crew_multiselect_dropdown_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        value: memberId > 0 ? [\n                                                            memberId.toString()\n                                                        ] : training === null || training === void 0 ? void 0 : training.Members,\n                                                        vesselID: vesselID,\n                                                        onChange: handleMemberChange\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 996,\n                                                        columnNumber: 37\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 994,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_18__.Separator, {\n                                                className: \"my-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 1006,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex w-full gap-4 mt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full \",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DateRange__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                mode: \"single\",\n                                                                onChange: handleTrainingDateChange,\n                                                                value: new Date(trainingDate)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1013,\n                                                                columnNumber: 41\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                className: \"text-destructive\",\n                                                                children: hasFormErrors && formErrors.Date\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1018,\n                                                                columnNumber: 41\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1008,\n                                                        columnNumber: 37\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full\",\n                                                        children: [\n                                                            vessels && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_16__.Combobox, {\n                                                                options: vessels.map((vessel)=>({\n                                                                        label: vessel.label,\n                                                                        value: vessel.value\n                                                                    })),\n                                                                defaultValues: (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Vessel\" ? rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_VesselID = rawTraining.VesselID) === null || _rawTraining_VesselID === void 0 ? void 0 : _rawTraining_VesselID.toString() : (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Onshore\" ? \"Desktop/shore\" : (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Other\" ? \"Other\" : (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Location\" && (rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_trainingLocation = rawTraining.trainingLocation) === null || _rawTraining_trainingLocation === void 0 ? void 0 : _rawTraining_trainingLocation.id) > 0 ? rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_trainingLocation1 = rawTraining.trainingLocation) === null || _rawTraining_trainingLocation1 === void 0 ? void 0 : _rawTraining_trainingLocation1.id.toString() : vesselId ? {\n                                                                    label: (_vessels_find = vessels.find((vessel)=>vessel.value === vesselId)) === null || _vessels_find === void 0 ? void 0 : _vessels_find.label,\n                                                                    value: vesselId.toString()\n                                                                } : null,\n                                                                isLoading: rawTraining,\n                                                                onChange: handleTrainingVesselChange,\n                                                                placeholder: \"Select location\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1024,\n                                                                columnNumber: 45\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                className: \"text-destructive\",\n                                                                children: hasFormErrors && formErrors.VesselID\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1068,\n                                                                columnNumber: 41\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1022,\n                                                        columnNumber: 37\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 1007,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"col-span-3 md:col-span-2\",\n                                                children: [\n                                                    getProcedures().length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-8\",\n                                                        children: getProcedures().map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_daily_check_field__WEBPACK_IMPORTED_MODULE_23__.CheckField, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                                                        label: type.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                        lineNumber: 1089,\n                                                                        columnNumber: 57\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_daily_check_field__WEBPACK_IMPORTED_MODULE_23__.CheckFieldContent, {\n                                                                        children: type.fields.map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_daily_check_field__WEBPACK_IMPORTED_MODULE_23__.DailyCheckField, {\n                                                                                displayField: field.status === \"Required\",\n                                                                                displayDescription: field.description,\n                                                                                displayLabel: field.fieldName,\n                                                                                inputId: field.id,\n                                                                                handleNoChange: ()=>handleProcedureChecks(field, type, false),\n                                                                                defaultNoChecked: getFieldStatus(field) === \"Not_Ok\",\n                                                                                handleYesChange: ()=>handleProcedureChecks(field, type, true),\n                                                                                defaultYesChecked: getFieldStatus(field) === \"Ok\",\n                                                                                commentAction: ()=>showCommentPopup(field),\n                                                                                comment: getComment(field),\n                                                                                displayImage: trainingID > 0,\n                                                                                fieldImages: fieldImages,\n                                                                                onImageUpload: refreshImages,\n                                                                                sectionData: {\n                                                                                    id: trainingID,\n                                                                                    sectionName: \"trainingSessionID\"\n                                                                                }\n                                                                            }, field.id, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                                lineNumber: 1097,\n                                                                                columnNumber: 69\n                                                                            }, undefined))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                        lineNumber: 1092,\n                                                                        columnNumber: 57\n                                                                    }, undefined)\n                                                                ]\n                                                            }, type.id, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1088,\n                                                                columnNumber: 53\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1085,\n                                                        columnNumber: 41\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"my-4 flex items-center w-full\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_editor__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            id: \"TrainingSummary\",\n                                                            placeholder: \"Summary of training, identify any outcomes, further training required or other observations.\",\n                                                            className: \"!w-full  ring-1 ring-inset \",\n                                                            handleEditorChange: handleEditorChange,\n                                                            content: content\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                            lineNumber: 1173,\n                                                            columnNumber: 41\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1172,\n                                                        columnNumber: 37\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 1083,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 968,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                lineNumber: 949,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_18__.Separator, {\n                                className: \"my-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                lineNumber: 1194,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid lg:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-3 md:col-span-1 md:my-4 \",\n                                        children: \"Signatures\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 1196,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-3 sm:col-span-2 md:my-4 flex justify-between flex-wrap gap-4\",\n                                        children: selectedMemberList && selectedMemberList.map((member, index)=>{\n                                            var _signatureMembers_find;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full md:w-96\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_signature_pad__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"w-full\",\n                                                    member: member.label,\n                                                    memberId: member.value,\n                                                    onSignatureChanged: (signature, member, memberId)=>onSignatureChanged(signature, member !== null && member !== void 0 ? member : \"\", memberId || 0),\n                                                    signature: {\n                                                        id: (_signatureMembers_find = signatureMembers.find((sig)=>sig.MemberID === member.value)) === null || _signatureMembers_find === void 0 ? void 0 : _signatureMembers_find.ID\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                    lineNumber: 1206,\n                                                    columnNumber: 49\n                                                }, undefined)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 1203,\n                                                columnNumber: 45\n                                            }, undefined);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 1199,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                lineNumber: 1195,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 948,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                lineNumber: 938,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_footer_wrapper__WEBPACK_IMPORTED_MODULE_22__.FooterWrapper, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_20__.Button, {\n                        variant: \"back\",\n                        onClick: ()=>router.push(\"/crew-training\"),\n                        iconLeft: _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"],\n                        children: \"Cancel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 1238,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_20__.Button, {\n                        onClick: handleSave,\n                        disabled: mutationCreateTrainingSessionLoading || mutationUpdateTrainingSessionLoading,\n                        children: trainingID === 0 ? \"Create session\" : \"Update session\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 1250,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                lineNumber: 1237,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_19__.Sheet, {\n                open: openViewProcedure,\n                onOpenChange: setOpenViewProcedure,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_19__.SheetContent, {\n                    side: \"right\",\n                    className: \"w-[400px] sm:w-[540px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_19__.SheetHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_19__.SheetTitle, {\n                                children: \"Procedures\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                lineNumber: 1262,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                            lineNumber: 1261,\n                            columnNumber: 21\n                        }, undefined),\n                        training && trainingTypes.filter((type)=>{\n                            var _training_TrainingTypes;\n                            return (training === null || training === void 0 ? void 0 : (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.includes(type.id)) && type.procedure;\n                        }).map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4 px-2.5 sm:px-5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_26__.H4, {\n                                        children: type.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 1277,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        dangerouslySetInnerHTML: {\n                                            __html: type.procedure\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 1279,\n                                        columnNumber: 37\n                                    }, undefined)\n                                ]\n                            }, type.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                lineNumber: 1274,\n                                columnNumber: 33\n                            }, undefined))\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                    lineNumber: 1260,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                lineNumber: 1259,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_27__.AlertDialog, {\n                open: openCommentAlert,\n                onOpenChange: setOpenCommentAlert,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_27__.AlertDialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_27__.AlertDialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_27__.AlertDialogTitle, {\n                                    children: \"Add Comment\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                    lineNumber: 1293,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_27__.AlertDialogDescription, {\n                                    children: \"Add a comment for this procedure check.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                    lineNumber: 1294,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                            lineNumber: 1292,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_28__.Textarea, {\n                            value: currentComment,\n                            onChange: (e)=>setCurrentComment(e.target.value),\n                            placeholder: \"Enter your comment here...\",\n                            rows: 4\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                            lineNumber: 1298,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_27__.AlertDialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_27__.AlertDialogCancel, {\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                    lineNumber: 1305,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_27__.AlertDialogAction, {\n                                    onClick: handleSaveComment,\n                                    children: \"Save Comment\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                    lineNumber: 1306,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                            lineNumber: 1304,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                    lineNumber: 1291,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                lineNumber: 1288,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(TrainingForm, \"Kpk999jA4FZirJIja2nOanr46Ls=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_25__.useToast,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation\n    ];\n});\n_c = TrainingForm;\n/* harmony default export */ __webpack_exports__[\"default\"] = (TrainingForm);\nvar _c;\n$RefreshReg$(_c, \"TrainingForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/create/form.tsx\n"));

/***/ })

});