"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/edit/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/create/form.tsx":
/*!**************************************************!*\
  !*** ./src/app/ui/crew-training/create/form.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_filter_components_crew_dropdown_crew_dropdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../../components/filter/components/crew-dropdown/crew-dropdown */ \"(app-pages-browser)/./src/components/filter/components/crew-dropdown/crew-dropdown.tsx\");\n/* harmony import */ var _components_skeletons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../../components/skeletons */ \"(app-pages-browser)/./src/components/skeletons.tsx\");\n/* harmony import */ var _type_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../type-multiselect-dropdown */ \"(app-pages-browser)/./src/app/ui/crew-training/type-multiselect-dropdown.tsx\");\n/* harmony import */ var _crew_multiselect_dropdown_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../crew/multiselect-dropdown/multiselect-dropdown */ \"(app-pages-browser)/./src/app/ui/crew/multiselect-dropdown/multiselect-dropdown.tsx\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _editor__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../../editor */ \"(app-pages-browser)/./src/app/ui/editor.tsx\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_loading__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/loading */ \"(app-pages-browser)/./src/app/loading.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_sheet__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/ui/sheet */ \"(app-pages-browser)/./src/components/ui/sheet.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_signature_pad__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/signature-pad */ \"(app-pages-browser)/./src/components/signature-pad.tsx\");\n/* harmony import */ var _components_footer_wrapper__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/footer-wrapper */ \"(app-pages-browser)/./src/components/footer-wrapper.tsx\");\n/* harmony import */ var _components_daily_check_field__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/daily-check-field */ \"(app-pages-browser)/./src/components/daily-check-field.tsx\");\n/* harmony import */ var _components_DateRange__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/DateRange */ \"(app-pages-browser)/./src/components/DateRange.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/components/ui/alert-dialog */ \"(app-pages-browser)/./src/components/ui/alert-dialog.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_list_header__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @/components/ui/list-header */ \"(app-pages-browser)/./src/components/ui/list-header.tsx\");\n/* harmony import */ var _queries__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! ./queries */ \"(app-pages-browser)/./src/app/ui/crew-training/create/queries.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst TrainingForm = (param)=>{\n    let { trainingID = 0, memberId = 0, trainingTypeId = 0, vesselId = 0 } = param;\n    var _rawTraining_VesselID, _rawTraining_trainingLocation, _rawTraining_trainingLocation1, _vessels_find;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_24__.useToast)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [training, setTraining] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({});\n    const [rawTraining, setRawTraining] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)();\n    const [trainingDate, setTrainingDate] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(new Date());\n    const [hasFormErrors, setHasFormErrors] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [selectedMemberList, setSelectedMemberList] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [signatureMembers, setSignatureMembers] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [vessels, setVessels] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)();\n    const [trainingTypes, setTrainingTypes] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [openViewProcedure, setOpenViewProcedure] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [currentComment, setCurrentComment] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [currentField, setCurrentField] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [bufferProcedureCheck, setBufferProcedureCheck] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [bufferFieldComment, setBufferFieldComment] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [openCommentAlert, setOpenCommentAlert] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [formErrors, setFormErrors] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        TrainingTypes: \"\",\n        TrainerID: \"\",\n        VesselID: \"\",\n        Date: \"\"\n    });\n    const [vesselID, setVesselID] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(vesselId);\n    const [fieldImages, setFieldImages] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_11__.getTrainingTypes)(setTrainingTypes);\n    const [getFieldImages] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_9__.GET_SECTION_MEMBER_IMAGES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readCaptureImages.nodes;\n            if (data) {\n                setFieldImages(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"getFieldImages error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        getFieldImages({\n            variables: {\n                filter: {\n                    trainingSessionID: {\n                        eq: trainingID\n                    }\n                }\n            }\n        });\n    }, []);\n    const refreshImages = async ()=>{\n        await getFieldImages({\n            variables: {\n                filter: {\n                    trainingSessionID: {\n                        eq: trainingID\n                    }\n                }\n            }\n        });\n    };\n    const handleSetTraining = (training)=>{\n        var _training_procedureFields;\n        const tDate = new Date(training.date);\n        setTrainingDate(tDate);\n        const trainingData = {\n            ID: trainingID,\n            Date: dayjs__WEBPACK_IMPORTED_MODULE_1___default()(training.date).format(\"YYYY-MM-DD\"),\n            Members: training.members.nodes.map((m)=>m.id),\n            TrainerID: training.trainer.id,\n            TrainingSummary: training.trainingSummary,\n            TrainingTypes: training.trainingTypes.nodes.map((t)=>t.id),\n            // VesselID: training.vessel.id,\n            VesselID: training.vesselID\n        };\n        setRawTraining(training);\n        setTraining(trainingData);\n        setContent(training.trainingSummary);\n        const members = training.members.nodes.map((m)=>{\n            var _m_firstName, _m_surname;\n            return {\n                label: \"\".concat((_m_firstName = m.firstName) !== null && _m_firstName !== void 0 ? _m_firstName : \"\", \" \").concat((_m_surname = m.surname) !== null && _m_surname !== void 0 ? _m_surname : \"\"),\n                value: m.id\n            };\n        }) || [];\n        const vesselCrewIds = training.vessel.seaLogsMembers.nodes.map((slm)=>+slm.id);\n        const vesselCrews = members.filter((m)=>vesselCrewIds.includes(+m.value));\n        setSelectedMemberList(vesselCrews);\n        const signatures = training.signatures.nodes.map((s)=>({\n                MemberID: s.member.id,\n                SignatureData: s.signatureData,\n                ID: s.id\n            }));\n        setSignatureMembers(signatures);\n        // Initialize buffer with existing procedure field data for updates\n        if ((_training_procedureFields = training.procedureFields) === null || _training_procedureFields === void 0 ? void 0 : _training_procedureFields.nodes) {\n            const existingProcedureChecks = training.procedureFields.nodes.map((field)=>({\n                    fieldId: field.customisedComponentFieldID,\n                    status: field.status === \"Ok\"\n                }));\n            setBufferProcedureCheck(existingProcedureChecks);\n            const existingFieldComments = training.procedureFields.nodes.filter((field)=>field.comment).map((field)=>({\n                    fieldId: field.customisedComponentFieldID,\n                    comment: field.comment\n                }));\n            setBufferFieldComment(existingFieldComments);\n        }\n    };\n    const handleSetVessels = (data)=>{\n        const activeVessels = data === null || data === void 0 ? void 0 : data.filter((vessel)=>!vessel.archived);\n        const formattedData = [\n            {\n                label: \"Other\",\n                value: \"Other\"\n            },\n            {\n                label: \"Desktop/shore\",\n                value: \"Onshore\"\n            },\n            ...activeVessels.map((vessel)=>({\n                    value: vessel.id,\n                    label: vessel.title\n                }))\n        ];\n        setVessels(formattedData);\n    };\n    const [queryVessels] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_29__.ReadVessels, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (queryVesselResponse)=>{\n            if (queryVesselResponse.readVessels.nodes) {\n                handleSetVessels(queryVesselResponse.readVessels.nodes);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryVessels error\", error);\n        }\n    });\n    const loadVessels = async ()=>{\n        await queryVessels({\n            variables: {\n                limit: 200,\n                offset: 0\n            }\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (isLoading) {\n            loadVessels();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_11__.getTrainingSessionByID)(trainingID, handleSetTraining);\n    const [mutationCreateTrainingSession, { loading: mutationCreateTrainingSessionLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.CREATE_TRAINING_SESSION, {\n        onCompleted: (response)=>{\n            const data = response.createTrainingSession;\n            if (data.id > 0) {\n                if (bufferProcedureCheck.length > 0) {\n                    const procedureFields = bufferProcedureCheck.map((procedureField)=>{\n                        var _bufferFieldComment_find;\n                        return {\n                            status: procedureField.status ? \"Ok\" : \"Not_Ok\",\n                            trainingSessionID: data.id,\n                            customisedComponentFieldID: procedureField.fieldId,\n                            comment: (_bufferFieldComment_find = bufferFieldComment.find((comment)=>comment.fieldId == procedureField.fieldId)) === null || _bufferFieldComment_find === void 0 ? void 0 : _bufferFieldComment_find.comment\n                        };\n                    });\n                    procedureFields.forEach((procedureField)=>{\n                        createCustomisedComponentFieldData({\n                            variables: {\n                                input: procedureField\n                            }\n                        });\n                    });\n                }\n                updateTrainingSessionDues();\n                updateSignatures(data.id);\n                handleEditorChange(data.trainingSummary);\n                router.push(\"/crew-training\");\n            } else {\n                console.error(\"mutationCreateUser error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationCreateTrainingSession error\", error);\n        }\n    });\n    const [mutationUpdateTrainingSession, { loading: mutationUpdateTrainingSessionLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.UPDATE_TRAINING_SESSION, {\n        onCompleted: (response)=>{\n            const data = response.updateTrainingSession;\n            if (data.id > 0) {\n                // Handle procedure checks for updates\n                if (bufferProcedureCheck.length > 0) {\n                    const procedureFields = bufferProcedureCheck.map((procedureField)=>{\n                        var _rawTraining_procedureFields_nodes, _rawTraining_procedureFields, _bufferFieldComment_find;\n                        const existingField = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : (_rawTraining_procedureFields_nodes = _rawTraining_procedureFields.nodes) === null || _rawTraining_procedureFields_nodes === void 0 ? void 0 : _rawTraining_procedureFields_nodes.find((field)=>field.customisedComponentFieldID === procedureField.fieldId);\n                        return {\n                            id: existingField === null || existingField === void 0 ? void 0 : existingField.id,\n                            status: procedureField.status ? \"Ok\" : \"Not_Ok\",\n                            trainingSessionID: data.id,\n                            customisedComponentFieldID: procedureField.fieldId,\n                            comment: (_bufferFieldComment_find = bufferFieldComment.find((comment)=>comment.fieldId == procedureField.fieldId)) === null || _bufferFieldComment_find === void 0 ? void 0 : _bufferFieldComment_find.comment\n                        };\n                    });\n                    procedureFields.forEach((procedureField)=>{\n                        if (procedureField.id) {\n                            // Update existing field\n                            updateCustomisedComponentFieldData({\n                                variables: {\n                                    input: procedureField\n                                }\n                            });\n                        } else {\n                            // Create new field\n                            const { id, ...createInput } = procedureField;\n                            createCustomisedComponentFieldData({\n                                variables: {\n                                    input: createInput\n                                }\n                            });\n                        }\n                    });\n                }\n                updateTrainingSessionDues();\n                updateSignatures(trainingID);\n                handleEditorChange(data.trainingSummary);\n                if (+memberId > 0) {\n                    router.push(\"/crew/info?id=\".concat(memberId));\n                } else if (+vesselId > 0) {\n                    router.push(\"/vessel/info?id=\".concat(vesselId));\n                } else {\n                    router.push(\"/crew-training\");\n                }\n            } else {\n                console.error(\"mutationUpdateTrainingSession error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationUpdateTrainingSession error\", error);\n        }\n    });\n    const [readOneTrainingSessionDue, { loading: readOneTrainingSessionDueLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_9__.READ_ONE_TRAINING_SESSION_DUE, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            return response.readOneTrainingSessionDue.data;\n        },\n        onError: (error)=>{\n            console.error(\"readOneTrainingSessionDueLoading error:\", error);\n            return null;\n        }\n    });\n    const getTrainingSessionDueWithVariables = async function() {\n        let variables = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {}, onCompleted = arguments.length > 1 ? arguments[1] : void 0;\n        const { data } = await readOneTrainingSessionDue({\n            variables: variables\n        });\n        onCompleted(data.readOneTrainingSessionDue);\n    };\n    const [mutationCreateTrainingSessionDue, { loading: createTrainingSessionDueLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.CREATE_TRAINING_SESSION_DUE, {\n        onCompleted: (response)=>{},\n        onError: (error)=>{\n            console.error(\"createTrainingSessionDue error\", error);\n        }\n    });\n    const [mutationUpdateTrainingSessionDue, { loading: updateTrainingSessionDueLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.UPDATE_TRAINING_SESSION_DUE, {\n        onCompleted: (response)=>{},\n        onError: (error)=>{\n            console.error(\"updateTrainingSessionDue error\", error);\n        }\n    });\n    const updateTrainingSessionDues = async ()=>{\n        const trainingSessionDues = [];\n        const vesselID = training.VesselID;\n        training.TrainingTypes.forEach((t)=>{\n            const trainingInfo = trainingTypes.find((tt)=>tt.id === t);\n            if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(trainingInfo) && trainingInfo.occursEvery > 0) {\n                const trainingTypeID = t;\n                const newDueDate = dayjs__WEBPACK_IMPORTED_MODULE_1___default()(training.Date).add(trainingInfo.occursEvery, \"day\");\n                training.Members.forEach((m)=>{\n                    const memberID = m;\n                    trainingSessionDues.push({\n                        dueDate: newDueDate.format(\"YYYY-MM-DD\"),\n                        memberID: memberID,\n                        vesselID: vesselID,\n                        trainingTypeID: trainingTypeID\n                    });\n                });\n            }\n        });\n        let trainingSessionDueWithIDs = [];\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(trainingSessionDues)) {\n            await Promise.all(trainingSessionDues.map(async (item)=>{\n                const variables = {\n                    filter: {\n                        memberID: {\n                            eq: item.memberID\n                        },\n                        vesselID: {\n                            eq: item.vesselID\n                        },\n                        trainingTypeID: {\n                            eq: item.trainingTypeID\n                        }\n                    }\n                };\n                const onCompleted = (response)=>{\n                    var _response_id;\n                    trainingSessionDueWithIDs.push({\n                        ...item,\n                        id: (_response_id = response === null || response === void 0 ? void 0 : response.id) !== null && _response_id !== void 0 ? _response_id : 0\n                    });\n                };\n                await getTrainingSessionDueWithVariables(variables, onCompleted);\n            }));\n        }\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(trainingSessionDueWithIDs)) {\n            await Promise.all(Array.from(trainingSessionDueWithIDs).map(async (item)=>{\n                const variables = {\n                    variables: {\n                        input: item\n                    }\n                };\n                if (item.id === 0) {\n                    await mutationCreateTrainingSessionDue(variables);\n                } else {\n                    await mutationUpdateTrainingSessionDue(variables);\n                }\n            }));\n        }\n    };\n    const [createCustomisedComponentFieldData] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.CREATE_CUSTOMISED_COMPONENT_FIELD_DATA, {\n        onCompleted: (response)=>{\n            var _rawTraining_procedureFields;\n            const data = response.createCustomisedComponentFieldData;\n            if (data.id > 0 && (rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : _rawTraining_procedureFields.nodes)) {\n                setRawTraining({\n                    ...rawTraining,\n                    procedureFields: {\n                        ...rawTraining.procedureFields,\n                        nodes: [\n                            ...rawTraining.procedureFields.nodes,\n                            data\n                        ]\n                    }\n                });\n            } else {\n                console.error(\"createCustomisedComponentFieldData error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"createCustomisedComponentFieldData error\", error);\n        }\n    });\n    const [updateCustomisedComponentFieldData] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.UPDATE_CUSTOMISED_COMPONENT_FIELD_DATA, {\n        onCompleted: (response)=>{\n            const data = response.updateCustomisedComponentFieldData;\n            if (data.id > 0) {\n                var _rawTraining_procedureFields;\n                setRawTraining({\n                    ...rawTraining,\n                    procedureFields: {\n                        ...rawTraining.procedureFields,\n                        nodes: [\n                            ...rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : _rawTraining_procedureFields.nodes.filter((procedureField)=>procedureField.customisedComponentFieldID !== data.customisedComponentFieldID),\n                            {\n                                ...data\n                            }\n                        ]\n                    }\n                });\n            } else {\n                console.error(\"updateCustomisedComponentFieldData error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"updateCustomisedComponentFieldData error\", error);\n        }\n    });\n    const handleSave = async ()=>{\n        var _training_Members, _training_TrainingTypes;\n        let hasErrors = false;\n        let errors = {\n            TrainingTypes: \"\",\n            TrainerID: \"\",\n            VesselID: \"\",\n            Date: \"\"\n        };\n        setFormErrors(errors);\n        if (lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(training.TrainingTypes)) {\n            hasErrors = true;\n            errors.TrainingTypes = \"Nature of training is required\";\n        }\n        if (!(training.TrainerID && training.TrainerID > 0)) {\n            hasErrors = true;\n            errors.TrainerID = \"Trainer is required\";\n        }\n        if (!training.VesselID && !(training.TrainingLocationID && training.TrainingLocationID >= 0)) {\n            hasErrors = true;\n            errors.VesselID = \"Location is required\";\n        }\n        if (typeof training.Date === \"undefined\") {\n            training.Date = dayjs__WEBPACK_IMPORTED_MODULE_1___default()().format(\"YYYY-MM-DD\");\n        }\n        if (training.Date === null || !dayjs__WEBPACK_IMPORTED_MODULE_1___default()(training.Date).isValid()) {\n            hasErrors = true;\n            errors.Date = \"The date is invalid\";\n        }\n        if (hasErrors) {\n            setHasFormErrors(true);\n            setFormErrors(errors);\n            toast({\n                title: \"Error\",\n                description: errors.TrainingTypes || errors.TrainerID || errors.VesselID || errors.Date,\n                variant: \"destructive\"\n            });\n            return;\n        }\n        const input = {\n            id: trainingID,\n            date: training.Date ? dayjs__WEBPACK_IMPORTED_MODULE_1___default()(training.Date).format(\"YYYY-MM-DD\") : \"\",\n            members: (_training_Members = training.Members) === null || _training_Members === void 0 ? void 0 : _training_Members.join(\",\"),\n            trainerID: training.TrainerID,\n            trainingSummary: content,\n            trainingTypes: (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.join(\",\"),\n            vesselID: training === null || training === void 0 ? void 0 : training.VesselID,\n            trainingLocationType: (training === null || training === void 0 ? void 0 : training.VesselID) ? training.VesselID === \"Other\" || training.VesselID === \"Onshore\" ? training.VesselID : \"Vessel\" : \"Location\"\n        };\n        if (trainingID === 0) {\n            await mutationCreateTrainingSession({\n                variables: {\n                    input: input\n                }\n            });\n        } else {\n            await mutationUpdateTrainingSession({\n                variables: {\n                    input: input\n                }\n            });\n        }\n    };\n    // var signatureCount = 0\n    const updateSignatures = (TrainingID)=>{\n        signatureMembers.length > 0 && (signatureMembers === null || signatureMembers === void 0 ? void 0 : signatureMembers.forEach((signature)=>{\n            checkAndSaveSignature(signature, TrainingID);\n        }));\n    };\n    const checkAndSaveSignature = async (signature, TrainingID)=>{\n        await queryGetMemberTrainingSignatures({\n            variables: {\n                filter: {\n                    memberID: {\n                        eq: signature.MemberID\n                    },\n                    trainingSessionID: {\n                        in: TrainingID\n                    }\n                }\n            }\n        }).then((response)=>{\n            const data = response.data.readMemberTraining_Signatures.nodes;\n            if (data.length > 0) {\n                mutationUpdateMemberTrainingSignature({\n                    variables: {\n                        input: {\n                            id: data[0].id,\n                            memberID: signature.MemberID,\n                            signatureData: signature.SignatureData,\n                            trainingSessionID: TrainingID\n                        }\n                    }\n                });\n            } else {\n                if (signature.SignatureData) {\n                    mutationCreateMemberTrainingSignature({\n                        variables: {\n                            input: {\n                                memberID: signature.MemberID,\n                                signatureData: signature.SignatureData,\n                                trainingSessionID: TrainingID\n                            }\n                        }\n                    });\n                }\n            }\n        }).catch((error)=>{\n            console.error(\"mutationGetMemberTrainingSignatures error\", error);\n        });\n    };\n    const [queryGetMemberTrainingSignatures] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_9__.GET_MEMBER_TRAINING_SIGNATURES);\n    const [mutationUpdateMemberTrainingSignature, { loading: mutationUpdateMemberTrainingSignatureLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.UPDATE_MEMBER_TRAINING_SIGNATURE, {\n        onCompleted: (response)=>{\n            const data = response.updateMemberTraining_Signature;\n            if (data.id > 0) {\n            // signatureCount++\n            // if (signatureCount === signatureMembers.length) {\n            // }\n            } else {\n                console.error(\"mutationUpdateMemberTrainingSignature error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationUpdateMemberTrainingSignature error\", error);\n        }\n    });\n    const [mutationCreateMemberTrainingSignature, { loading: mutationCreateMemberTrainingSignatureLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.CREATE_MEMBER_TRAINING_SIGNATURE, {\n        onCompleted: (response)=>{\n            const data = response.createMemberTraining_Signature;\n            if (data.id > 0) {\n            // signatureCount++\n            // if (signatureCount === signatureMembers.length) {\n            // }\n            } else {\n                console.error(\"mutationCreateMemberTrainingSignature error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationCreateMemberTrainingSignature error\", error);\n        }\n    });\n    const handleTrainingDateChange = (date)=>{\n        setTrainingDate(date && new Date(date.toString()));\n        setTraining({\n            ...training,\n            Date: dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"YYYY-MM-DD\")\n        });\n    };\n    const handleTrainerChange = (trainer)=>{\n        if (!trainer) return; // Add early return if trainer is null\n        // Use Set() to prevent duplicate values, then Array.from() to convert it to an array\n        const membersSet = new Set((training === null || training === void 0 ? void 0 : training.Members) || []);\n        membersSet.add(trainer.value);\n        const members = Array.from(membersSet);\n        setTraining({\n            ...training,\n            TrainerID: trainer.value,\n            Members: members\n        });\n        setSelectedMemberList([\n            ...selectedMemberList,\n            trainer\n        ]);\n        setSignatureMembers([\n            ...signatureMembers,\n            {\n                MemberID: +trainer.value,\n                SignatureData: null\n            }\n        ]);\n    };\n    const handleTrainingTypeChange = (trainingTypes)=>{\n        setTraining({\n            ...training,\n            TrainingTypes: trainingTypes.map((item)=>item.value)\n        });\n    };\n    /* const handleTrainingLocationChange = (vessel: any) => {\r\n        setTraining({\r\n            ...training,\r\n            VesselID: vessel.isVessel ? vessel.value : 0,\r\n            TrainingLocationID: !vessel.isVessel ? vessel.value : 0,\r\n        })\r\n    } */ const handleMemberChange = (members)=>{\n        const signatures = signatureMembers.filter((item)=>members.some((m)=>+m.value === item.MemberID));\n        setTraining({\n            ...training,\n            Members: members.map((item)=>item.value)\n        });\n        setSelectedMemberList(members);\n        setSignatureMembers(signatures);\n    };\n    const onSignatureChanged = (e, member, memberId)=>{\n        const index = signatureMembers.findIndex((object)=>object.MemberID === memberId);\n        const updatedMembers = [\n            ...signatureMembers\n        ];\n        if (e) {\n            if (index !== -1) {\n                if (e.trim() === \"\") {\n                    updatedMembers.splice(index, 1);\n                } else {\n                    updatedMembers[index].SignatureData = e;\n                }\n            } else {\n                updatedMembers.push({\n                    MemberID: memberId,\n                    SignatureData: e\n                });\n            }\n        } else {\n            updatedMembers.splice(index, 1);\n        }\n        setSignatureMembers(updatedMembers);\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_11__.getTrainingTypeByID)(trainingTypeId, setTraining);\n    const handleTrainingVesselChange = (vessel)=>{\n        setTraining({\n            ...training,\n            VesselID: vessel ? typeof vessel === \"object\" && !Array.isArray(vessel) ? vessel.value : 0 : 0\n        });\n    };\n    const handleEditorChange = (newContent)=>{\n        setContent(newContent);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(training)) {\n            const vid = vesselId > 0 || isNaN(parseInt(training === null || training === void 0 ? void 0 : training.VesselID, 10)) ? vesselId : parseInt(training === null || training === void 0 ? void 0 : training.VesselID, 10);\n            setVesselID(vid);\n        }\n    }, [\n        vesselId,\n        training\n    ]);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_13__.getPermissions);\n    }, []);\n    if (!permissions || !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_13__.hasPermission)(\"EDIT_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_13__.hasPermission)(\"VIEW_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_13__.hasPermission)(\"RECORD_TRAINING\", permissions)) {\n        return !permissions ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n            lineNumber: 838,\n            columnNumber: 13\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n            errorMessage: \"Oops You do not have the permission to view this section.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n            lineNumber: 840,\n            columnNumber: 13\n        }, undefined);\n    }\n    const getProcedures = ()=>{\n        const procedures = trainingTypes.filter((type)=>{\n            var _training_TrainingTypes;\n            return training === null || training === void 0 ? void 0 : (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.includes(type.id);\n        });\n        return procedures.map((type)=>{\n            var _this;\n            return type.customisedComponentField.nodes.length > 0 ? {\n                id: type.id,\n                title: type.title,\n                fields: (_this = [\n                    ...type.customisedComponentField.nodes\n                ]) === null || _this === void 0 ? void 0 : _this.sort((a, b)=>a.sortOrder - b.sortOrder)\n            } : null;\n        }).filter((type)=>type != null);\n    };\n    const handleProcedureChecks = (field, type, status)=>{\n        // Always use buffer system for consistency, whether creating or updating\n        const procedureCheck = bufferProcedureCheck.filter((procedureField)=>procedureField.fieldId !== field.id);\n        setBufferProcedureCheck([\n            ...procedureCheck,\n            {\n                fieldId: field.id,\n                status: status\n            }\n        ]);\n    };\n    const getFieldStatus = (field)=>{\n        var _rawTraining_procedureFields_nodes, _rawTraining_procedureFields;\n        if (bufferProcedureCheck.length > 0) {\n            const fieldStatus = bufferProcedureCheck.find((procedureField)=>procedureField.fieldId == field.id);\n            if (fieldStatus) {\n                return fieldStatus.status ? \"Ok\" : \"Not_Ok\";\n            }\n        }\n        const fieldStatus = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : (_rawTraining_procedureFields_nodes = _rawTraining_procedureFields.nodes) === null || _rawTraining_procedureFields_nodes === void 0 ? void 0 : _rawTraining_procedureFields_nodes.find((procedureField)=>procedureField.customisedComponentFieldID == field.id);\n        return (fieldStatus === null || fieldStatus === void 0 ? void 0 : fieldStatus.status) || \"\";\n    };\n    const showCommentPopup = (field)=>{\n        var _rawTraining_procedureFields_nodes, _rawTraining_procedureFields;\n        const fieldComment = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : (_rawTraining_procedureFields_nodes = _rawTraining_procedureFields.nodes) === null || _rawTraining_procedureFields_nodes === void 0 ? void 0 : _rawTraining_procedureFields_nodes.find((procedureField)=>procedureField.customisedComponentFieldID == field.id);\n        if (bufferFieldComment.length > 0) {\n            const fieldComment = bufferFieldComment.find((procedureField)=>procedureField.fieldId == field.id);\n            setCurrentComment((fieldComment === null || fieldComment === void 0 ? void 0 : fieldComment.comment) || \"\");\n        } else {\n            setCurrentComment((fieldComment === null || fieldComment === void 0 ? void 0 : fieldComment.comment) || \"\");\n        }\n        setCurrentField(field);\n        setOpenCommentAlert(true);\n    };\n    const getComment = (field)=>{\n        var _rawTraining_procedureFields_nodes, _rawTraining_procedureFields;\n        if (bufferFieldComment.length > 0) {\n            const fieldComment = bufferFieldComment.find((procedureField)=>procedureField.fieldId == field.id);\n            if (fieldComment) {\n                return fieldComment.comment;\n            }\n        }\n        const fieldComment = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : (_rawTraining_procedureFields_nodes = _rawTraining_procedureFields.nodes) === null || _rawTraining_procedureFields_nodes === void 0 ? void 0 : _rawTraining_procedureFields_nodes.find((procedureField)=>procedureField.customisedComponentFieldID == field.id);\n        return (fieldComment === null || fieldComment === void 0 ? void 0 : fieldComment.comment) || field.comment;\n    };\n    const handleSaveComment = ()=>{\n        // Always use buffer system for consistency, whether creating or updating\n        const fieldComment = bufferFieldComment.filter((procedureField)=>procedureField.fieldId !== currentField.id);\n        setBufferFieldComment([\n            ...fieldComment,\n            {\n                fieldId: currentField.id,\n                comment: currentComment\n            }\n        ]);\n        setOpenCommentAlert(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_list_header__WEBPACK_IMPORTED_MODULE_28__.ListHeader, {\n                title: \"\".concat(trainingID === 0 ? \"New\" : \"Edit\", \" Training Session\"),\n                actions: training && trainingTypes.filter((type)=>{\n                    var _training_TrainingTypes;\n                    return (training === null || training === void 0 ? void 0 : (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.includes(type.id)) && type.procedure;\n                }).length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_19__.Button, {\n                    onClick: ()=>setOpenViewProcedure(true),\n                    children: \"View Procedures\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                    lineNumber: 948,\n                    columnNumber: 25\n                }, void 0) : undefined\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                lineNumber: 939,\n                columnNumber: 13\n            }, undefined),\n            !training && trainingID > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_5__.TrainingSessionFormSkeleton, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                lineNumber: 956,\n                columnNumber: 17\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-16 space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_17__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_17__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_17__.CardTitle, {\n                                    children: \"Training Details\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                    lineNumber: 962,\n                                    columnNumber: 29\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                lineNumber: 961,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_17__.CardContent, {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full my-4 flex flex-col\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                                label: \"Trainer\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_crew_dropdown_crew_dropdown__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    value: training === null || training === void 0 ? void 0 : training.TrainerID,\n                                                    vesselID: vesselID,\n                                                    onChange: handleTrainerChange\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                    lineNumber: 967,\n                                                    columnNumber: 37\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 966,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                className: \"text-destructive\",\n                                                children: hasFormErrors && formErrors.TrainerID\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 973,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 965,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full flex flex-col\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_type_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                value: training === null || training === void 0 ? void 0 : training.TrainingTypes,\n                                                onChange: handleTrainingTypeChange\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 979,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                className: \"text-red-vivid-500\",\n                                                children: hasFormErrors && formErrors.TrainingTypes\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 983,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 978,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full flex flex-col\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                                children: \"Crew\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 989,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_crew_multiselect_dropdown_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                value: memberId > 0 ? [\n                                                    memberId.toString()\n                                                ] : training === null || training === void 0 ? void 0 : training.Members,\n                                                vesselID: vesselID,\n                                                onChange: handleMemberChange\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 990,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 988,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex w-full gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DateRange__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        mode: \"single\",\n                                                        onChange: handleTrainingDateChange,\n                                                        value: new Date(trainingDate)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1003,\n                                                        columnNumber: 37\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                        className: \"text-destructive\",\n                                                        children: hasFormErrors && formErrors.Date\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1008,\n                                                        columnNumber: 37\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 1002,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full\",\n                                                children: [\n                                                    vessels && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_16__.Combobox, {\n                                                        options: vessels.map((vessel)=>({\n                                                                label: vessel.label,\n                                                                value: vessel.value\n                                                            })),\n                                                        defaultValues: (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Vessel\" ? rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_VesselID = rawTraining.VesselID) === null || _rawTraining_VesselID === void 0 ? void 0 : _rawTraining_VesselID.toString() : (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Onshore\" ? \"Desktop/shore\" : (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Other\" ? \"Other\" : (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Location\" && (rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_trainingLocation = rawTraining.trainingLocation) === null || _rawTraining_trainingLocation === void 0 ? void 0 : _rawTraining_trainingLocation.id) > 0 ? rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_trainingLocation1 = rawTraining.trainingLocation) === null || _rawTraining_trainingLocation1 === void 0 ? void 0 : _rawTraining_trainingLocation1.id.toString() : vesselId ? {\n                                                            label: (_vessels_find = vessels.find((vessel)=>vessel.value === vesselId)) === null || _vessels_find === void 0 ? void 0 : _vessels_find.label,\n                                                            value: vesselId.toString()\n                                                        } : null,\n                                                        isLoading: rawTraining,\n                                                        onChange: handleTrainingVesselChange,\n                                                        placeholder: \"Select location\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1014,\n                                                        columnNumber: 41\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                        className: \"text-destructive\",\n                                                        children: hasFormErrors && formErrors.VesselID\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1057,\n                                                        columnNumber: 37\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 1012,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 1001,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                lineNumber: 964,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 960,\n                        columnNumber: 21\n                    }, undefined),\n                    getProcedures().length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_17__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_17__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_17__.CardTitle, {\n                                    children: \"Procedures\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                    lineNumber: 1069,\n                                    columnNumber: 33\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                lineNumber: 1068,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_17__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-8\",\n                                    children: getProcedures().map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_daily_check_field__WEBPACK_IMPORTED_MODULE_22__.CheckField, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                                    label: type.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                    lineNumber: 1075,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_daily_check_field__WEBPACK_IMPORTED_MODULE_22__.CheckFieldContent, {\n                                                    children: type.fields.map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_daily_check_field__WEBPACK_IMPORTED_MODULE_22__.DailyCheckField, {\n                                                            displayField: field.status === \"Required\",\n                                                            displayDescription: field.description,\n                                                            displayLabel: field.fieldName,\n                                                            inputId: field.id,\n                                                            handleNoChange: ()=>handleProcedureChecks(field, type, false),\n                                                            defaultNoChecked: getFieldStatus(field) === \"Not_Ok\",\n                                                            handleYesChange: ()=>handleProcedureChecks(field, type, true),\n                                                            defaultYesChecked: getFieldStatus(field) === \"Ok\",\n                                                            commentAction: ()=>showCommentPopup(field),\n                                                            comment: getComment(field),\n                                                            displayImage: trainingID > 0,\n                                                            fieldImages: fieldImages,\n                                                            onImageUpload: refreshImages,\n                                                            sectionData: {\n                                                                id: trainingID,\n                                                                sectionName: \"trainingSessionID\"\n                                                            }\n                                                        }, field.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                            lineNumber: 1079,\n                                                            columnNumber: 57\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                    lineNumber: 1076,\n                                                    columnNumber: 45\n                                                }, undefined)\n                                            ]\n                                        }, type.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                            lineNumber: 1074,\n                                            columnNumber: 41\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                    lineNumber: 1072,\n                                    columnNumber: 33\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                lineNumber: 1071,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 1067,\n                        columnNumber: 25\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_17__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_17__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_17__.CardTitle, {\n                                    children: \"Training Summary\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                    lineNumber: 1152,\n                                    columnNumber: 29\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                lineNumber: 1151,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_17__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_editor__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    id: \"TrainingSummary\",\n                                    placeholder: \"Summary of training, identify any outcomes, further training required or other observations.\",\n                                    className: \"!w-full ring-1 ring-inset\",\n                                    handleEditorChange: handleEditorChange,\n                                    content: content\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                    lineNumber: 1155,\n                                    columnNumber: 29\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                lineNumber: 1154,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 1150,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_17__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_17__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_17__.CardTitle, {\n                                    children: \"Signatures\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                    lineNumber: 1168,\n                                    columnNumber: 29\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                lineNumber: 1167,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_17__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-1 justify-between flex-wrap gap-4\",\n                                    children: selectedMemberList && selectedMemberList.map((member, index)=>{\n                                        var _signatureMembers_find;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full md:w-96\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_signature_pad__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"w-full\",\n                                                member: member.label,\n                                                memberId: member.value,\n                                                onSignatureChanged: (signature, member, memberId)=>onSignatureChanged(signature, member !== null && member !== void 0 ? member : \"\", memberId || 0),\n                                                signature: {\n                                                    id: (_signatureMembers_find = signatureMembers.find((sig)=>sig.MemberID === member.value)) === null || _signatureMembers_find === void 0 ? void 0 : _signatureMembers_find.ID\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 1178,\n                                                columnNumber: 49\n                                            }, undefined)\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                            lineNumber: 1175,\n                                            columnNumber: 45\n                                        }, undefined);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                    lineNumber: 1171,\n                                    columnNumber: 29\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                lineNumber: 1170,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 1166,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                lineNumber: 958,\n                columnNumber: 17\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_footer_wrapper__WEBPACK_IMPORTED_MODULE_21__.FooterWrapper, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_19__.Button, {\n                        variant: \"back\",\n                        onClick: ()=>router.push(\"/crew-training\"),\n                        iconLeft: _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"],\n                        children: \"Cancel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 1210,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_19__.Button, {\n                        onClick: handleSave,\n                        disabled: mutationCreateTrainingSessionLoading || mutationUpdateTrainingSessionLoading,\n                        children: trainingID === 0 ? \"Create session\" : \"Update session\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 1222,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                lineNumber: 1209,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_18__.Sheet, {\n                open: openViewProcedure,\n                onOpenChange: setOpenViewProcedure,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_18__.SheetContent, {\n                    side: \"right\",\n                    className: \"w-[400px] sm:w-[540px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_18__.SheetHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_18__.SheetTitle, {\n                                children: \"Procedures\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                lineNumber: 1234,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                            lineNumber: 1233,\n                            columnNumber: 21\n                        }, undefined),\n                        training && trainingTypes.filter((type)=>{\n                            var _training_TrainingTypes;\n                            return (training === null || training === void 0 ? void 0 : (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.includes(type.id)) && type.procedure;\n                        }).map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4 px-2.5 sm:px-5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_25__.H4, {\n                                        children: type.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 1249,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        dangerouslySetInnerHTML: {\n                                            __html: type.procedure\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 1251,\n                                        columnNumber: 37\n                                    }, undefined)\n                                ]\n                            }, type.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                lineNumber: 1246,\n                                columnNumber: 33\n                            }, undefined))\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                    lineNumber: 1232,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                lineNumber: 1231,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_26__.AlertDialog, {\n                open: openCommentAlert,\n                onOpenChange: setOpenCommentAlert,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_26__.AlertDialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_26__.AlertDialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_26__.AlertDialogTitle, {\n                                    children: \"Add Comment\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                    lineNumber: 1265,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_26__.AlertDialogDescription, {\n                                    children: \"Add a comment for this procedure check.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                    lineNumber: 1266,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                            lineNumber: 1264,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_27__.Textarea, {\n                            value: currentComment,\n                            onChange: (e)=>setCurrentComment(e.target.value),\n                            placeholder: \"Enter your comment here...\",\n                            rows: 4\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                            lineNumber: 1270,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_26__.AlertDialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_26__.AlertDialogCancel, {\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                    lineNumber: 1277,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_26__.AlertDialogAction, {\n                                    onClick: handleSaveComment,\n                                    children: \"Save Comment\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                    lineNumber: 1278,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                            lineNumber: 1276,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                    lineNumber: 1263,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                lineNumber: 1260,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(TrainingForm, \"Kpk999jA4FZirJIja2nOanr46Ls=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_24__.useToast,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation\n    ];\n});\n_c = TrainingForm;\n/* harmony default export */ __webpack_exports__[\"default\"] = (TrainingForm);\nvar _c;\n$RefreshReg$(_c, \"TrainingForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/create/form.tsx\n"));

/***/ })

});