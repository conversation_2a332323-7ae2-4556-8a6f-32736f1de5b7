"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/edit/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/create/form.tsx":
/*!**************************************************!*\
  !*** ./src/app/ui/crew-training/create/form.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_filter_components_crew_dropdown_crew_dropdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../../components/filter/components/crew-dropdown/crew-dropdown */ \"(app-pages-browser)/./src/components/filter/components/crew-dropdown/crew-dropdown.tsx\");\n/* harmony import */ var _components_skeletons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../../components/skeletons */ \"(app-pages-browser)/./src/components/skeletons.tsx\");\n/* harmony import */ var _type_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../type-multiselect-dropdown */ \"(app-pages-browser)/./src/app/ui/crew-training/type-multiselect-dropdown.tsx\");\n/* harmony import */ var _crew_multiselect_dropdown_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../crew/multiselect-dropdown/multiselect-dropdown */ \"(app-pages-browser)/./src/app/ui/crew/multiselect-dropdown/multiselect-dropdown.tsx\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _editor__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../../editor */ \"(app-pages-browser)/./src/app/ui/editor.tsx\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_loading__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/loading */ \"(app-pages-browser)/./src/app/loading.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_sheet__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/ui/sheet */ \"(app-pages-browser)/./src/components/ui/sheet.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_signature_pad__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/signature-pad */ \"(app-pages-browser)/./src/components/signature-pad.tsx\");\n/* harmony import */ var _components_footer_wrapper__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/footer-wrapper */ \"(app-pages-browser)/./src/components/footer-wrapper.tsx\");\n/* harmony import */ var _components_daily_check_field__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/daily-check-field */ \"(app-pages-browser)/./src/components/daily-check-field.tsx\");\n/* harmony import */ var _components_DateRange__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/DateRange */ \"(app-pages-browser)/./src/components/DateRange.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @/components/ui/alert-dialog */ \"(app-pages-browser)/./src/components/ui/alert-dialog.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _queries__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! ./queries */ \"(app-pages-browser)/./src/app/ui/crew-training/create/queries.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst TrainingForm = (param)=>{\n    let { trainingID = 0, memberId = 0, trainingTypeId = 0, vesselId = 0 } = param;\n    var _rawTraining_VesselID, _rawTraining_trainingLocation, _rawTraining_trainingLocation1, _vessels_find;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_25__.useToast)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [training, setTraining] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({});\n    const [rawTraining, setRawTraining] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)();\n    const [trainingDate, setTrainingDate] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(new Date());\n    const [hasFormErrors, setHasFormErrors] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [selectedMemberList, setSelectedMemberList] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [signatureMembers, setSignatureMembers] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [vessels, setVessels] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)();\n    const [trainingTypes, setTrainingTypes] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [openViewProcedure, setOpenViewProcedure] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [currentComment, setCurrentComment] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [currentField, setCurrentField] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [bufferProcedureCheck, setBufferProcedureCheck] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [bufferFieldComment, setBufferFieldComment] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [openCommentAlert, setOpenCommentAlert] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [formErrors, setFormErrors] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        TrainingTypes: \"\",\n        TrainerID: \"\",\n        VesselID: \"\",\n        Date: \"\"\n    });\n    const [vesselID, setVesselID] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(vesselId);\n    const [fieldImages, setFieldImages] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_11__.getTrainingTypes)(setTrainingTypes);\n    const [getFieldImages] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_9__.GET_SECTION_MEMBER_IMAGES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readCaptureImages.nodes;\n            if (data) {\n                setFieldImages(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"getFieldImages error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        getFieldImages({\n            variables: {\n                filter: {\n                    trainingSessionID: {\n                        eq: trainingID\n                    }\n                }\n            }\n        });\n    }, []);\n    const refreshImages = async ()=>{\n        await getFieldImages({\n            variables: {\n                filter: {\n                    trainingSessionID: {\n                        eq: trainingID\n                    }\n                }\n            }\n        });\n    };\n    const handleSetTraining = (training)=>{\n        var _training_procedureFields;\n        const tDate = new Date(training.date);\n        setTrainingDate(tDate);\n        const trainingData = {\n            ID: trainingID,\n            Date: dayjs__WEBPACK_IMPORTED_MODULE_1___default()(training.date).format(\"YYYY-MM-DD\"),\n            Members: training.members.nodes.map((m)=>m.id),\n            TrainerID: training.trainer.id,\n            TrainingSummary: training.trainingSummary,\n            TrainingTypes: training.trainingTypes.nodes.map((t)=>t.id),\n            // VesselID: training.vessel.id,\n            VesselID: training.vesselID\n        };\n        setRawTraining(training);\n        setTraining(trainingData);\n        setContent(training.trainingSummary);\n        const members = training.members.nodes.map((m)=>{\n            var _m_firstName, _m_surname;\n            return {\n                label: \"\".concat((_m_firstName = m.firstName) !== null && _m_firstName !== void 0 ? _m_firstName : \"\", \" \").concat((_m_surname = m.surname) !== null && _m_surname !== void 0 ? _m_surname : \"\"),\n                value: m.id\n            };\n        }) || [];\n        const vesselCrewIds = training.vessel.seaLogsMembers.nodes.map((slm)=>+slm.id);\n        const vesselCrews = members.filter((m)=>vesselCrewIds.includes(+m.value));\n        setSelectedMemberList(vesselCrews);\n        const signatures = training.signatures.nodes.map((s)=>({\n                MemberID: s.member.id,\n                SignatureData: s.signatureData,\n                ID: s.id\n            }));\n        setSignatureMembers(signatures);\n        // Initialize buffer with existing procedure field data for updates\n        if ((_training_procedureFields = training.procedureFields) === null || _training_procedureFields === void 0 ? void 0 : _training_procedureFields.nodes) {\n            const existingProcedureChecks = training.procedureFields.nodes.map((field)=>({\n                    fieldId: field.customisedComponentFieldID,\n                    status: field.status === \"Ok\"\n                }));\n            setBufferProcedureCheck(existingProcedureChecks);\n            const existingFieldComments = training.procedureFields.nodes.filter((field)=>field.comment).map((field)=>({\n                    fieldId: field.customisedComponentFieldID,\n                    comment: field.comment\n                }));\n            setBufferFieldComment(existingFieldComments);\n        }\n    };\n    const handleSetVessels = (data)=>{\n        const activeVessels = data === null || data === void 0 ? void 0 : data.filter((vessel)=>!vessel.archived);\n        const formattedData = [\n            {\n                label: \"Other\",\n                value: \"Other\"\n            },\n            {\n                label: \"Desktop/shore\",\n                value: \"Onshore\"\n            },\n            ...activeVessels.map((vessel)=>({\n                    value: vessel.id,\n                    label: vessel.title\n                }))\n        ];\n        setVessels(formattedData);\n    };\n    const [queryVessels] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_29__.ReadVessels, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (queryVesselResponse)=>{\n            if (queryVesselResponse.readVessels.nodes) {\n                handleSetVessels(queryVesselResponse.readVessels.nodes);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryVessels error\", error);\n        }\n    });\n    const loadVessels = async ()=>{\n        await queryVessels({\n            variables: {\n                limit: 200,\n                offset: 0\n            }\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (isLoading) {\n            loadVessels();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_11__.getTrainingSessionByID)(trainingID, handleSetTraining);\n    const [mutationCreateTrainingSession, { loading: mutationCreateTrainingSessionLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.CREATE_TRAINING_SESSION, {\n        onCompleted: (response)=>{\n            const data = response.createTrainingSession;\n            if (data.id > 0) {\n                if (bufferProcedureCheck.length > 0) {\n                    const procedureFields = bufferProcedureCheck.map((procedureField)=>{\n                        var _bufferFieldComment_find;\n                        return {\n                            status: procedureField.status ? \"Ok\" : \"Not_Ok\",\n                            trainingSessionID: data.id,\n                            customisedComponentFieldID: procedureField.fieldId,\n                            comment: (_bufferFieldComment_find = bufferFieldComment.find((comment)=>comment.fieldId == procedureField.fieldId)) === null || _bufferFieldComment_find === void 0 ? void 0 : _bufferFieldComment_find.comment\n                        };\n                    });\n                    procedureFields.forEach((procedureField)=>{\n                        createCustomisedComponentFieldData({\n                            variables: {\n                                input: procedureField\n                            }\n                        });\n                    });\n                }\n                updateTrainingSessionDues();\n                updateSignatures(data.id);\n                handleEditorChange(data.trainingSummary);\n                router.push(\"/crew-training\");\n            } else {\n                console.error(\"mutationCreateUser error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationCreateTrainingSession error\", error);\n        }\n    });\n    const [mutationUpdateTrainingSession, { loading: mutationUpdateTrainingSessionLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.UPDATE_TRAINING_SESSION, {\n        onCompleted: (response)=>{\n            const data = response.updateTrainingSession;\n            if (data.id > 0) {\n                // Handle procedure checks for updates\n                if (bufferProcedureCheck.length > 0) {\n                    const procedureFields = bufferProcedureCheck.map((procedureField)=>{\n                        var _rawTraining_procedureFields_nodes, _rawTraining_procedureFields, _bufferFieldComment_find;\n                        const existingField = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : (_rawTraining_procedureFields_nodes = _rawTraining_procedureFields.nodes) === null || _rawTraining_procedureFields_nodes === void 0 ? void 0 : _rawTraining_procedureFields_nodes.find((field)=>field.customisedComponentFieldID === procedureField.fieldId);\n                        return {\n                            id: existingField === null || existingField === void 0 ? void 0 : existingField.id,\n                            status: procedureField.status ? \"Ok\" : \"Not_Ok\",\n                            trainingSessionID: data.id,\n                            customisedComponentFieldID: procedureField.fieldId,\n                            comment: (_bufferFieldComment_find = bufferFieldComment.find((comment)=>comment.fieldId == procedureField.fieldId)) === null || _bufferFieldComment_find === void 0 ? void 0 : _bufferFieldComment_find.comment\n                        };\n                    });\n                    procedureFields.forEach((procedureField)=>{\n                        if (procedureField.id) {\n                            // Update existing field\n                            updateCustomisedComponentFieldData({\n                                variables: {\n                                    input: procedureField\n                                }\n                            });\n                        } else {\n                            // Create new field\n                            const { id, ...createInput } = procedureField;\n                            createCustomisedComponentFieldData({\n                                variables: {\n                                    input: createInput\n                                }\n                            });\n                        }\n                    });\n                }\n                updateTrainingSessionDues();\n                updateSignatures(trainingID);\n                handleEditorChange(data.trainingSummary);\n                if (+memberId > 0) {\n                    router.push(\"/crew/info?id=\".concat(memberId));\n                } else if (+vesselId > 0) {\n                    router.push(\"/vessel/info?id=\".concat(vesselId));\n                } else {\n                    router.push(\"/crew-training\");\n                }\n            } else {\n                console.error(\"mutationUpdateTrainingSession error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationUpdateTrainingSession error\", error);\n        }\n    });\n    const [readOneTrainingSessionDue, { loading: readOneTrainingSessionDueLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_9__.READ_ONE_TRAINING_SESSION_DUE, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            return response.readOneTrainingSessionDue.data;\n        },\n        onError: (error)=>{\n            console.error(\"readOneTrainingSessionDueLoading error:\", error);\n            return null;\n        }\n    });\n    const getTrainingSessionDueWithVariables = async function() {\n        let variables = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {}, onCompleted = arguments.length > 1 ? arguments[1] : void 0;\n        const { data } = await readOneTrainingSessionDue({\n            variables: variables\n        });\n        onCompleted(data.readOneTrainingSessionDue);\n    };\n    const [mutationCreateTrainingSessionDue, { loading: createTrainingSessionDueLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.CREATE_TRAINING_SESSION_DUE, {\n        onCompleted: (response)=>{},\n        onError: (error)=>{\n            console.error(\"createTrainingSessionDue error\", error);\n        }\n    });\n    const [mutationUpdateTrainingSessionDue, { loading: updateTrainingSessionDueLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.UPDATE_TRAINING_SESSION_DUE, {\n        onCompleted: (response)=>{},\n        onError: (error)=>{\n            console.error(\"updateTrainingSessionDue error\", error);\n        }\n    });\n    const updateTrainingSessionDues = async ()=>{\n        const trainingSessionDues = [];\n        const vesselID = training.VesselID;\n        training.TrainingTypes.forEach((t)=>{\n            const trainingInfo = trainingTypes.find((tt)=>tt.id === t);\n            if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(trainingInfo) && trainingInfo.occursEvery > 0) {\n                const trainingTypeID = t;\n                const newDueDate = dayjs__WEBPACK_IMPORTED_MODULE_1___default()(training.Date).add(trainingInfo.occursEvery, \"day\");\n                training.Members.forEach((m)=>{\n                    const memberID = m;\n                    trainingSessionDues.push({\n                        dueDate: newDueDate.format(\"YYYY-MM-DD\"),\n                        memberID: memberID,\n                        vesselID: vesselID,\n                        trainingTypeID: trainingTypeID\n                    });\n                });\n            }\n        });\n        let trainingSessionDueWithIDs = [];\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(trainingSessionDues)) {\n            await Promise.all(trainingSessionDues.map(async (item)=>{\n                const variables = {\n                    filter: {\n                        memberID: {\n                            eq: item.memberID\n                        },\n                        vesselID: {\n                            eq: item.vesselID\n                        },\n                        trainingTypeID: {\n                            eq: item.trainingTypeID\n                        }\n                    }\n                };\n                const onCompleted = (response)=>{\n                    var _response_id;\n                    trainingSessionDueWithIDs.push({\n                        ...item,\n                        id: (_response_id = response === null || response === void 0 ? void 0 : response.id) !== null && _response_id !== void 0 ? _response_id : 0\n                    });\n                };\n                await getTrainingSessionDueWithVariables(variables, onCompleted);\n            }));\n        }\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(trainingSessionDueWithIDs)) {\n            await Promise.all(Array.from(trainingSessionDueWithIDs).map(async (item)=>{\n                const variables = {\n                    variables: {\n                        input: item\n                    }\n                };\n                if (item.id === 0) {\n                    await mutationCreateTrainingSessionDue(variables);\n                } else {\n                    await mutationUpdateTrainingSessionDue(variables);\n                }\n            }));\n        }\n    };\n    const [createCustomisedComponentFieldData] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.CREATE_CUSTOMISED_COMPONENT_FIELD_DATA, {\n        onCompleted: (response)=>{\n            var _rawTraining_procedureFields;\n            const data = response.createCustomisedComponentFieldData;\n            if (data.id > 0 && (rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : _rawTraining_procedureFields.nodes)) {\n                setRawTraining({\n                    ...rawTraining,\n                    procedureFields: {\n                        ...rawTraining.procedureFields,\n                        nodes: [\n                            ...rawTraining.procedureFields.nodes,\n                            data\n                        ]\n                    }\n                });\n            } else {\n                console.error(\"createCustomisedComponentFieldData error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"createCustomisedComponentFieldData error\", error);\n        }\n    });\n    const [updateCustomisedComponentFieldData] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.UPDATE_CUSTOMISED_COMPONENT_FIELD_DATA, {\n        onCompleted: (response)=>{\n            const data = response.updateCustomisedComponentFieldData;\n            if (data.id > 0) {\n                var _rawTraining_procedureFields;\n                setRawTraining({\n                    ...rawTraining,\n                    procedureFields: {\n                        ...rawTraining.procedureFields,\n                        nodes: [\n                            ...rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : _rawTraining_procedureFields.nodes.filter((procedureField)=>procedureField.customisedComponentFieldID !== data.customisedComponentFieldID),\n                            {\n                                ...data\n                            }\n                        ]\n                    }\n                });\n            } else {\n                console.error(\"updateCustomisedComponentFieldData error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"updateCustomisedComponentFieldData error\", error);\n        }\n    });\n    const handleSave = async ()=>{\n        var _training_Members, _training_TrainingTypes;\n        let hasErrors = false;\n        let errors = {\n            TrainingTypes: \"\",\n            TrainerID: \"\",\n            VesselID: \"\",\n            Date: \"\"\n        };\n        setFormErrors(errors);\n        if (lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(training.TrainingTypes)) {\n            hasErrors = true;\n            errors.TrainingTypes = \"Nature of training is required\";\n        }\n        if (!(training.TrainerID && training.TrainerID > 0)) {\n            hasErrors = true;\n            errors.TrainerID = \"Trainer is required\";\n        }\n        if (!training.VesselID && !(training.TrainingLocationID && training.TrainingLocationID >= 0)) {\n            hasErrors = true;\n            errors.VesselID = \"Location is required\";\n        }\n        if (typeof training.Date === \"undefined\") {\n            training.Date = dayjs__WEBPACK_IMPORTED_MODULE_1___default()().format(\"YYYY-MM-DD\");\n        }\n        if (training.Date === null || !dayjs__WEBPACK_IMPORTED_MODULE_1___default()(training.Date).isValid()) {\n            hasErrors = true;\n            errors.Date = \"The date is invalid\";\n        }\n        if (hasErrors) {\n            setHasFormErrors(true);\n            setFormErrors(errors);\n            toast({\n                title: \"Error\",\n                description: errors.TrainingTypes || errors.TrainerID || errors.VesselID || errors.Date,\n                variant: \"destructive\"\n            });\n            return;\n        }\n        const input = {\n            id: trainingID,\n            date: training.Date ? dayjs__WEBPACK_IMPORTED_MODULE_1___default()(training.Date).format(\"YYYY-MM-DD\") : \"\",\n            members: (_training_Members = training.Members) === null || _training_Members === void 0 ? void 0 : _training_Members.join(\",\"),\n            trainerID: training.TrainerID,\n            trainingSummary: content,\n            trainingTypes: (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.join(\",\"),\n            vesselID: training === null || training === void 0 ? void 0 : training.VesselID,\n            trainingLocationType: (training === null || training === void 0 ? void 0 : training.VesselID) ? training.VesselID === \"Other\" || training.VesselID === \"Onshore\" ? training.VesselID : \"Vessel\" : \"Location\"\n        };\n        if (trainingID === 0) {\n            await mutationCreateTrainingSession({\n                variables: {\n                    input: input\n                }\n            });\n        } else {\n            await mutationUpdateTrainingSession({\n                variables: {\n                    input: input\n                }\n            });\n        }\n    };\n    // var signatureCount = 0\n    const updateSignatures = (TrainingID)=>{\n        signatureMembers.length > 0 && (signatureMembers === null || signatureMembers === void 0 ? void 0 : signatureMembers.forEach((signature)=>{\n            checkAndSaveSignature(signature, TrainingID);\n        }));\n    };\n    const checkAndSaveSignature = async (signature, TrainingID)=>{\n        await queryGetMemberTrainingSignatures({\n            variables: {\n                filter: {\n                    memberID: {\n                        eq: signature.MemberID\n                    },\n                    trainingSessionID: {\n                        in: TrainingID\n                    }\n                }\n            }\n        }).then((response)=>{\n            const data = response.data.readMemberTraining_Signatures.nodes;\n            if (data.length > 0) {\n                mutationUpdateMemberTrainingSignature({\n                    variables: {\n                        input: {\n                            id: data[0].id,\n                            memberID: signature.MemberID,\n                            signatureData: signature.SignatureData,\n                            trainingSessionID: TrainingID\n                        }\n                    }\n                });\n            } else {\n                if (signature.SignatureData) {\n                    mutationCreateMemberTrainingSignature({\n                        variables: {\n                            input: {\n                                memberID: signature.MemberID,\n                                signatureData: signature.SignatureData,\n                                trainingSessionID: TrainingID\n                            }\n                        }\n                    });\n                }\n            }\n        }).catch((error)=>{\n            console.error(\"mutationGetMemberTrainingSignatures error\", error);\n        });\n    };\n    const [queryGetMemberTrainingSignatures] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_9__.GET_MEMBER_TRAINING_SIGNATURES);\n    const [mutationUpdateMemberTrainingSignature, { loading: mutationUpdateMemberTrainingSignatureLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.UPDATE_MEMBER_TRAINING_SIGNATURE, {\n        onCompleted: (response)=>{\n            const data = response.updateMemberTraining_Signature;\n            if (data.id > 0) {\n            // signatureCount++\n            // if (signatureCount === signatureMembers.length) {\n            // }\n            } else {\n                console.error(\"mutationUpdateMemberTrainingSignature error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationUpdateMemberTrainingSignature error\", error);\n        }\n    });\n    const [mutationCreateMemberTrainingSignature, { loading: mutationCreateMemberTrainingSignatureLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.CREATE_MEMBER_TRAINING_SIGNATURE, {\n        onCompleted: (response)=>{\n            const data = response.createMemberTraining_Signature;\n            if (data.id > 0) {\n            // signatureCount++\n            // if (signatureCount === signatureMembers.length) {\n            // }\n            } else {\n                console.error(\"mutationCreateMemberTrainingSignature error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationCreateMemberTrainingSignature error\", error);\n        }\n    });\n    const handleTrainingDateChange = (date)=>{\n        setTrainingDate(date && new Date(date.toString()));\n        setTraining({\n            ...training,\n            Date: dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"YYYY-MM-DD\")\n        });\n    };\n    const handleTrainerChange = (trainer)=>{\n        if (!trainer) return; // Add early return if trainer is null\n        // Use Set() to prevent duplicate values, then Array.from() to convert it to an array\n        const membersSet = new Set((training === null || training === void 0 ? void 0 : training.Members) || []);\n        membersSet.add(trainer.value);\n        const members = Array.from(membersSet);\n        setTraining({\n            ...training,\n            TrainerID: trainer.value,\n            Members: members\n        });\n        setSelectedMemberList([\n            ...selectedMemberList,\n            trainer\n        ]);\n        setSignatureMembers([\n            ...signatureMembers,\n            {\n                MemberID: +trainer.value,\n                SignatureData: null\n            }\n        ]);\n    };\n    const handleTrainingTypeChange = (trainingTypes)=>{\n        setTraining({\n            ...training,\n            TrainingTypes: trainingTypes.map((item)=>item.value)\n        });\n    };\n    /* const handleTrainingLocationChange = (vessel: any) => {\r\n        setTraining({\r\n            ...training,\r\n            VesselID: vessel.isVessel ? vessel.value : 0,\r\n            TrainingLocationID: !vessel.isVessel ? vessel.value : 0,\r\n        })\r\n    } */ const handleMemberChange = (members)=>{\n        const signatures = signatureMembers.filter((item)=>members.some((m)=>+m.value === item.MemberID));\n        setTraining({\n            ...training,\n            Members: members.map((item)=>item.value)\n        });\n        setSelectedMemberList(members);\n        setSignatureMembers(signatures);\n    };\n    const onSignatureChanged = (e, member, memberId)=>{\n        const index = signatureMembers.findIndex((object)=>object.MemberID === memberId);\n        const updatedMembers = [\n            ...signatureMembers\n        ];\n        if (e) {\n            if (index !== -1) {\n                if (e.trim() === \"\") {\n                    updatedMembers.splice(index, 1);\n                } else {\n                    updatedMembers[index].SignatureData = e;\n                }\n            } else {\n                updatedMembers.push({\n                    MemberID: memberId,\n                    SignatureData: e\n                });\n            }\n        } else {\n            updatedMembers.splice(index, 1);\n        }\n        setSignatureMembers(updatedMembers);\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_11__.getTrainingTypeByID)(trainingTypeId, setTraining);\n    const handleTrainingVesselChange = (vessel)=>{\n        setTraining({\n            ...training,\n            VesselID: vessel ? typeof vessel === \"object\" && !Array.isArray(vessel) ? vessel.value : 0 : 0\n        });\n    };\n    const handleEditorChange = (newContent)=>{\n        setContent(newContent);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(training)) {\n            const vid = vesselId > 0 || isNaN(parseInt(training === null || training === void 0 ? void 0 : training.VesselID, 10)) ? vesselId : parseInt(training === null || training === void 0 ? void 0 : training.VesselID, 10);\n            setVesselID(vid);\n        }\n    }, [\n        vesselId,\n        training\n    ]);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_13__.getPermissions);\n    }, []);\n    if (!permissions || !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_13__.hasPermission)(\"EDIT_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_13__.hasPermission)(\"VIEW_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_13__.hasPermission)(\"RECORD_TRAINING\", permissions)) {\n        return !permissions ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n            lineNumber: 837,\n            columnNumber: 13\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n            errorMessage: \"Oops You do not have the permission to view this section.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n            lineNumber: 839,\n            columnNumber: 13\n        }, undefined);\n    }\n    const getProcedures = ()=>{\n        const procedures = trainingTypes.filter((type)=>{\n            var _training_TrainingTypes;\n            return training === null || training === void 0 ? void 0 : (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.includes(type.id);\n        });\n        return procedures.map((type)=>{\n            var _this;\n            return type.customisedComponentField.nodes.length > 0 ? {\n                id: type.id,\n                title: type.title,\n                fields: (_this = [\n                    ...type.customisedComponentField.nodes\n                ]) === null || _this === void 0 ? void 0 : _this.sort((a, b)=>a.sortOrder - b.sortOrder)\n            } : null;\n        }).filter((type)=>type != null);\n    };\n    const handleProcedureChecks = (field, type, status)=>{\n        // Always use buffer system for consistency, whether creating or updating\n        const procedureCheck = bufferProcedureCheck.filter((procedureField)=>procedureField.fieldId !== field.id);\n        setBufferProcedureCheck([\n            ...procedureCheck,\n            {\n                fieldId: field.id,\n                status: status\n            }\n        ]);\n    };\n    const getFieldStatus = (field)=>{\n        var _rawTraining_procedureFields_nodes, _rawTraining_procedureFields;\n        if (bufferProcedureCheck.length > 0) {\n            const fieldStatus = bufferProcedureCheck.find((procedureField)=>procedureField.fieldId == field.id);\n            if (fieldStatus) {\n                return fieldStatus.status ? \"Ok\" : \"Not_Ok\";\n            }\n        }\n        const fieldStatus = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : (_rawTraining_procedureFields_nodes = _rawTraining_procedureFields.nodes) === null || _rawTraining_procedureFields_nodes === void 0 ? void 0 : _rawTraining_procedureFields_nodes.find((procedureField)=>procedureField.customisedComponentFieldID == field.id);\n        return (fieldStatus === null || fieldStatus === void 0 ? void 0 : fieldStatus.status) || \"\";\n    };\n    const showCommentPopup = (field)=>{\n        var _rawTraining_procedureFields_nodes, _rawTraining_procedureFields;\n        const fieldComment = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : (_rawTraining_procedureFields_nodes = _rawTraining_procedureFields.nodes) === null || _rawTraining_procedureFields_nodes === void 0 ? void 0 : _rawTraining_procedureFields_nodes.find((procedureField)=>procedureField.customisedComponentFieldID == field.id);\n        if (bufferFieldComment.length > 0) {\n            const fieldComment = bufferFieldComment.find((procedureField)=>procedureField.fieldId == field.id);\n            setCurrentComment((fieldComment === null || fieldComment === void 0 ? void 0 : fieldComment.comment) || \"\");\n        } else {\n            setCurrentComment((fieldComment === null || fieldComment === void 0 ? void 0 : fieldComment.comment) || \"\");\n        }\n        setCurrentField(field);\n        setOpenCommentAlert(true);\n    };\n    const getComment = (field)=>{\n        var _rawTraining_procedureFields_nodes, _rawTraining_procedureFields;\n        if (bufferFieldComment.length > 0) {\n            const fieldComment = bufferFieldComment.find((procedureField)=>procedureField.fieldId == field.id);\n            if (fieldComment) {\n                return fieldComment.comment;\n            }\n        }\n        const fieldComment = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : (_rawTraining_procedureFields_nodes = _rawTraining_procedureFields.nodes) === null || _rawTraining_procedureFields_nodes === void 0 ? void 0 : _rawTraining_procedureFields_nodes.find((procedureField)=>procedureField.customisedComponentFieldID == field.id);\n        return (fieldComment === null || fieldComment === void 0 ? void 0 : fieldComment.comment) || field.comment;\n    };\n    const handleSaveComment = ()=>{\n        // Always use buffer system for consistency, whether creating or updating\n        const fieldComment = bufferFieldComment.filter((procedureField)=>procedureField.fieldId !== currentField.id);\n        setBufferFieldComment([\n            ...fieldComment,\n            {\n                fieldId: currentField.id,\n                comment: currentComment\n            }\n        ]);\n        setOpenCommentAlert(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_17__.Card, {\n                className: \"mb-2.5 mx-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_17__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_17__.CardTitle, {\n                            children: [\n                                trainingID === 0 ? \"New\" : \"Edit\",\n                                \" Training Session\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                            lineNumber: 940,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 939,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_18__.Separator, {\n                        className: \"my-5\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 944,\n                        columnNumber: 17\n                    }, undefined),\n                    !training && trainingID > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_5__.TrainingSessionFormSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 946,\n                        columnNumber: 21\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_17__.CardContent, {\n                        className: \"p-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid lg:grid-cols-3 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-3 md:col-span-1 my-4 \",\n                                        children: [\n                                            \"Training Details\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \" mt-4 max-w-[25rem] leading-loose mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 952,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            training && trainingTypes.filter((type)=>{\n                                                var _training_TrainingTypes;\n                                                return (training === null || training === void 0 ? void 0 : (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.includes(type.id)) && type.procedure;\n                                            }).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_20__.Button, {\n                                                onClick: ()=>setOpenViewProcedure(true),\n                                                children: \"View Procedures\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 960,\n                                                columnNumber: 41\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 950,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-3 md:col-span-2 pt-8 pb-5 space-y-6 px-7 border border-border border-dashed rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full my-4 flex flex-col\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                                                label: \"Trainer\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_crew_dropdown_crew_dropdown__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    value: training === null || training === void 0 ? void 0 : training.TrainerID,\n                                                                    vesselID: vesselID,\n                                                                    onChange: handleTrainerChange\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                    lineNumber: 972,\n                                                                    columnNumber: 45\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 971,\n                                                                columnNumber: 41\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                className: \"text-destructive\",\n                                                                children: hasFormErrors && formErrors.TrainerID\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 978,\n                                                                columnNumber: 41\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 970,\n                                                        columnNumber: 37\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full md:mt-4 flex flex-col\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_type_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                value: training === null || training === void 0 ? void 0 : training.TrainingTypes,\n                                                                onChange: handleTrainingTypeChange\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 984,\n                                                                columnNumber: 41\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                className: \"text-red-vivid-500\",\n                                                                children: hasFormErrors && formErrors.TrainingTypes\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 988,\n                                                                columnNumber: 41\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 983,\n                                                        columnNumber: 37\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 969,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full mt-4 flex flex-col\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                                        children: \"Crew\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 995,\n                                                        columnNumber: 37\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_crew_multiselect_dropdown_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        value: memberId > 0 ? [\n                                                            memberId.toString()\n                                                        ] : training === null || training === void 0 ? void 0 : training.Members,\n                                                        vesselID: vesselID,\n                                                        onChange: handleMemberChange\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 996,\n                                                        columnNumber: 37\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 994,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_18__.Separator, {\n                                                className: \"my-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 1006,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex w-full gap-4 mt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full \",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DateRange__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                mode: \"single\",\n                                                                onChange: handleTrainingDateChange,\n                                                                value: new Date(trainingDate)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1013,\n                                                                columnNumber: 41\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                className: \"text-destructive\",\n                                                                children: hasFormErrors && formErrors.Date\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1018,\n                                                                columnNumber: 41\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1008,\n                                                        columnNumber: 37\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full\",\n                                                        children: [\n                                                            vessels && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_16__.Combobox, {\n                                                                options: vessels.map((vessel)=>({\n                                                                        label: vessel.label,\n                                                                        value: vessel.value\n                                                                    })),\n                                                                defaultValues: (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Vessel\" ? rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_VesselID = rawTraining.VesselID) === null || _rawTraining_VesselID === void 0 ? void 0 : _rawTraining_VesselID.toString() : (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Onshore\" ? \"Desktop/shore\" : (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Other\" ? \"Other\" : (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Location\" && (rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_trainingLocation = rawTraining.trainingLocation) === null || _rawTraining_trainingLocation === void 0 ? void 0 : _rawTraining_trainingLocation.id) > 0 ? rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_trainingLocation1 = rawTraining.trainingLocation) === null || _rawTraining_trainingLocation1 === void 0 ? void 0 : _rawTraining_trainingLocation1.id.toString() : vesselId ? {\n                                                                    label: (_vessels_find = vessels.find((vessel)=>vessel.value === vesselId)) === null || _vessels_find === void 0 ? void 0 : _vessels_find.label,\n                                                                    value: vesselId.toString()\n                                                                } : null,\n                                                                isLoading: rawTraining,\n                                                                onChange: handleTrainingVesselChange,\n                                                                placeholder: \"Select location\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1024,\n                                                                columnNumber: 45\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                className: \"text-destructive\",\n                                                                children: hasFormErrors && formErrors.VesselID\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1068,\n                                                                columnNumber: 41\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1022,\n                                                        columnNumber: 37\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 1007,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"col-span-3 md:col-span-2\",\n                                                children: [\n                                                    getProcedures().length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-8\",\n                                                        children: getProcedures().map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_daily_check_field__WEBPACK_IMPORTED_MODULE_23__.CheckField, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                                                        label: type.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                        lineNumber: 1089,\n                                                                        columnNumber: 57\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_daily_check_field__WEBPACK_IMPORTED_MODULE_23__.CheckFieldContent, {\n                                                                        children: type.fields.map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_daily_check_field__WEBPACK_IMPORTED_MODULE_23__.DailyCheckField, {\n                                                                                displayField: field.status === \"Required\",\n                                                                                displayDescription: field.description,\n                                                                                displayLabel: field.fieldName,\n                                                                                inputId: field.id,\n                                                                                handleNoChange: ()=>handleProcedureChecks(field, type, false),\n                                                                                defaultNoChecked: getFieldStatus(field) === \"Not_Ok\",\n                                                                                handleYesChange: ()=>handleProcedureChecks(field, type, true),\n                                                                                defaultYesChecked: getFieldStatus(field) === \"Ok\",\n                                                                                commentAction: ()=>showCommentPopup(field),\n                                                                                comment: getComment(field),\n                                                                                displayImage: trainingID > 0,\n                                                                                fieldImages: fieldImages,\n                                                                                onImageUpload: refreshImages,\n                                                                                sectionData: {\n                                                                                    id: trainingID,\n                                                                                    sectionName: \"trainingSessionID\"\n                                                                                }\n                                                                            }, field.id, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                                lineNumber: 1097,\n                                                                                columnNumber: 69\n                                                                            }, undefined))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                        lineNumber: 1092,\n                                                                        columnNumber: 57\n                                                                    }, undefined)\n                                                                ]\n                                                            }, type.id, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1088,\n                                                                columnNumber: 53\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1085,\n                                                        columnNumber: 41\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"my-4 flex items-center w-full\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_editor__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            id: \"TrainingSummary\",\n                                                            placeholder: \"Summary of training, identify any outcomes, further training required or other observations.\",\n                                                            className: \"!w-full  ring-1 ring-inset \",\n                                                            handleEditorChange: handleEditorChange,\n                                                            content: content\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                            lineNumber: 1173,\n                                                            columnNumber: 41\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1172,\n                                                        columnNumber: 37\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 1083,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 968,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                lineNumber: 949,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_18__.Separator, {\n                                className: \"my-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                lineNumber: 1194,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid lg:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-3 md:col-span-1 md:my-4 \",\n                                        children: \"Signatures\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 1196,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-3 sm:col-span-2 md:my-4 flex justify-between flex-wrap gap-4\",\n                                        children: selectedMemberList && selectedMemberList.map((member, index)=>{\n                                            var _signatureMembers_find;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full md:w-96\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_signature_pad__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"w-full\",\n                                                    member: member.label,\n                                                    memberId: member.value,\n                                                    onSignatureChanged: (signature, member, memberId)=>onSignatureChanged(signature, member !== null && member !== void 0 ? member : \"\", memberId || 0),\n                                                    signature: {\n                                                        id: (_signatureMembers_find = signatureMembers.find((sig)=>sig.MemberID === member.value)) === null || _signatureMembers_find === void 0 ? void 0 : _signatureMembers_find.ID\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                    lineNumber: 1206,\n                                                    columnNumber: 49\n                                                }, undefined)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                lineNumber: 1203,\n                                                columnNumber: 45\n                                            }, undefined);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 1199,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                lineNumber: 1195,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 948,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                lineNumber: 938,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_footer_wrapper__WEBPACK_IMPORTED_MODULE_22__.FooterWrapper, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_20__.Button, {\n                        variant: \"back\",\n                        onClick: ()=>router.push(\"/crew-training\"),\n                        iconLeft: _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"],\n                        children: \"Cancel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 1238,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_20__.Button, {\n                        onClick: handleSave,\n                        disabled: mutationCreateTrainingSessionLoading || mutationUpdateTrainingSessionLoading,\n                        children: trainingID === 0 ? \"Create session\" : \"Update session\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 1250,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                lineNumber: 1237,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_19__.Sheet, {\n                open: openViewProcedure,\n                onOpenChange: setOpenViewProcedure,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_19__.SheetContent, {\n                    side: \"right\",\n                    className: \"w-[400px] sm:w-[540px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_19__.SheetHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_19__.SheetTitle, {\n                                children: \"Procedures\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                lineNumber: 1262,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                            lineNumber: 1261,\n                            columnNumber: 21\n                        }, undefined),\n                        training && trainingTypes.filter((type)=>{\n                            var _training_TrainingTypes;\n                            return (training === null || training === void 0 ? void 0 : (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.includes(type.id)) && type.procedure;\n                        }).map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4 px-2.5 sm:px-5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_26__.H4, {\n                                        children: type.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 1277,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        dangerouslySetInnerHTML: {\n                                            __html: type.procedure\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 1279,\n                                        columnNumber: 37\n                                    }, undefined)\n                                ]\n                            }, type.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                lineNumber: 1274,\n                                columnNumber: 33\n                            }, undefined))\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                    lineNumber: 1260,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                lineNumber: 1259,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_27__.AlertDialog, {\n                open: openCommentAlert,\n                onOpenChange: setOpenCommentAlert,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_27__.AlertDialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_27__.AlertDialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_27__.AlertDialogTitle, {\n                                    children: \"Add Comment\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                    lineNumber: 1293,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_27__.AlertDialogDescription, {\n                                    children: \"Add a comment for this procedure check.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                    lineNumber: 1294,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                            lineNumber: 1292,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_28__.Textarea, {\n                            value: currentComment,\n                            onChange: (e)=>setCurrentComment(e.target.value),\n                            placeholder: \"Enter your comment here...\",\n                            rows: 4\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                            lineNumber: 1298,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_27__.AlertDialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_27__.AlertDialogCancel, {\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                    lineNumber: 1305,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_27__.AlertDialogAction, {\n                                    onClick: handleSaveComment,\n                                    children: \"Save Comment\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                    lineNumber: 1306,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                            lineNumber: 1304,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                    lineNumber: 1291,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                lineNumber: 1288,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(TrainingForm, \"Kpk999jA4FZirJIja2nOanr46Ls=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_25__.useToast,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation\n    ];\n});\n_c = TrainingForm;\n/* harmony default export */ __webpack_exports__[\"default\"] = (TrainingForm);\nvar _c;\n$RefreshReg$(_c, \"TrainingForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/create/form.tsx\n"));

/***/ })

});